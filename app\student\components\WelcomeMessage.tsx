'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { X, <PERSON><PERSON><PERSON>, Brain, Zap } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { usePlayerData, useTimelineData } from '../store/dashboardStore'

interface WelcomeMessageProps {
  onDismiss?: () => void
}

export default function WelcomeMessage({ onDismiss }: WelcomeMessageProps) {
  const [isVisible, setIsVisible] = useState(true)
  const [currentTip, setCurrentTip] = useState(0)
  
  const playerData = usePlayerData()
  const timelineData = useTimelineData()

  const tips = [
    {
      icon: Brain,
      title: "Neural Synchronization",
      message: "Your consciousness energy is flowing at optimal levels. Ready to explore new dimensions of awareness.",
      color: "text-neural-cyan"
    },
    {
      icon: Sparkles,
      title: "Timeline Evolution",
      message: `Your Timeline has ${timelineData?.nodes?.length || 0} nodes available. Collaborate with others to accelerate collective growth.`,
      color: "text-quantum-purple"
    },
    {
      icon: Zap,
      title: "Quantum Stability",
      message: "Maintain balance between exploration and focus. Your quantum field is harmonizing beautifully.",
      color: "text-flame-orange"
    }
  ]

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTip((prev) => (prev + 1) % tips.length)
    }, 4000)

    return () => clearInterval(interval)
  }, [tips.length])

  const handleDismiss = () => {
    setIsVisible(false)
    setTimeout(() => {
      onDismiss?.()
    }, 300)
  }

  if (!isVisible) return null

  const currentTipData = tips[currentTip]
  const Icon = currentTipData.icon

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: -20, scale: 0.95 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        exit={{ opacity: 0, y: -20, scale: 0.95 }}
        className="fixed top-20 right-6 w-80 z-40"
      >
        <div className="gaming-panel-inner p-4 rounded-lg border border-neural-cyan/20 backdrop-blur-md">
          {/* Header */}
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-gradient-to-br from-neural-cyan to-quantum-purple rounded-lg flex items-center justify-center">
                <Sparkles className="w-4 h-4 text-white" />
              </div>
              <div>
                <h3 className="text-sm font-orbitron font-bold text-neural-cyan">
                  Welcome, {playerData?.username || 'Player'}
                </h3>
                <p className="text-xs text-white/60">Level {playerData?.level || 1} NanoArchitect</p>
              </div>
            </div>
            <Button size="sm" variant="ghost" onClick={handleDismiss}>
              <X className="w-4 h-4" />
            </Button>
          </div>

          {/* Rotating Tips */}
          <AnimatePresence mode="wait">
            <motion.div
              key={currentTip}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
              className="space-y-3"
            >
              <div className="flex items-center gap-2">
                <Icon className={`w-5 h-5 ${currentTipData.color}`} />
                <h4 className={`text-sm font-bold ${currentTipData.color}`}>
                  {currentTipData.title}
                </h4>
              </div>
              <p className="text-sm text-white/80 leading-relaxed">
                {currentTipData.message}
              </p>
            </motion.div>
          </AnimatePresence>

          {/* Progress Indicators */}
          <div className="flex gap-1 mt-4 justify-center">
            {tips.map((_, index) => (
              <div
                key={index}
                className={`w-2 h-2 rounded-full transition-all duration-300 ${
                  index === currentTip ? 'bg-neural-cyan' : 'bg-white/20'
                }`}
              />
            ))}
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-2 gap-3 mt-4 pt-3 border-t border-white/10">
            <div className="text-center">
              <div className="text-lg font-bold text-neural-cyan">{playerData?.xp || 0}%</div>
              <div className="text-xs text-white/60">XP Level</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-quantum-purple">{playerData?.level || 1}</div>
              <div className="text-xs text-white/60">QS Level</div>
            </div>
          </div>

          {/* Action Button */}
          <Button 
            variant="quantum" 
            className="w-full mt-4" 
            size="sm"
            glow
            onClick={handleDismiss}
          >
            Begin Quantum Journey
          </Button>
        </div>

        {/* Glow Effect */}
        <motion.div
          className="absolute inset-0 pointer-events-none border border-neural-cyan/20 rounded-lg"
          animate={{
            boxShadow: [
              '0 0 20px rgba(34, 211, 238, 0.2)',
              '0 0 30px rgba(139, 92, 246, 0.3)',
              '0 0 20px rgba(34, 211, 238, 0.2)'
            ]
          }}
          transition={{
            duration: 3,
            repeat: Infinity,
            ease: 'easeInOut'
          }}
        />
      </motion.div>
    </AnimatePresence>
  )
}
