'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Brain, 
  Users, 
  Heart, 
  Cpu,
  Play,
  Pause,
  RotateCcw,
  Send,
  Sparkles,
  Zap,
  BookOpen,
  MessageCircle,
  Volume2,
  VolumeX
} from 'lucide-react'

import { <PERSON><PERSON>, <PERSON>alogContent, <PERSON>alogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Card } from '@/components/ui/card'

// Types
interface NodeInteractionModalProps {
  nodeId: string | null
  onClose: () => void
}

interface NodeConfig {
  type: 'learning' | 'echo' | 'meditation' | 'timelinegen'
  title: string
  icon: any
  color: string
  description: string
}

// Learning Node Interface
function LearningNodeInterface({ nodeId }: { nodeId: string }) {
  const [currentLesson, setCurrentLesson] = useState(0)
  const [progress, setProgress] = useState(0)
  const [isPlaying, setIsPlaying] = useState(false)
  
  const lessons = [
    {
      title: "Quantum Consciousness Basics",
      description: "Understanding the fundamental principles of consciousness energy",
      duration: "5 min",
      completed: true
    },
    {
      title: "Neural Network Synchronization",
      description: "Learn how to sync with the Timeline's collective consciousness",
      duration: "8 min",
      completed: false
    },
    {
      title: "DNA Evolution Mechanics",
      description: "How your actions influence genetic expression",
      duration: "12 min",
      completed: false
    }
  ]
  
  useEffect(() => {
    if (isPlaying) {
      const interval = setInterval(() => {
        setProgress(prev => {
          if (prev >= 100) {
            setIsPlaying(false)
            return 100
          }
          return prev + 2
        })
      }, 100)
      
      return () => clearInterval(interval)
    }
  }, [isPlaying])
  
  return (
    <div className="space-y-6">
      <div className="text-center">
        <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-neural-cyan/20 flex items-center justify-center">
          <Brain className="w-8 h-8 text-neural-cyan" />
        </div>
        <h3 className="text-xl font-orbitron font-bold text-neural-cyan mb-2">Neural Learning Interface</h3>
        <p className="text-sm text-white/60">AI-curated consciousness expansion</p>
      </div>
      
      {/* Current Lesson */}
      <Card variant="quantum" className="p-4">
        <div className="flex items-center justify-between mb-3">
          <h4 className="font-bold text-white">{lessons[currentLesson].title}</h4>
          <span className="text-xs text-white/60">{lessons[currentLesson].duration}</span>
        </div>
        <p className="text-sm text-white/70 mb-4">{lessons[currentLesson].description}</p>
        
        <div className="space-y-3">
          <Progress value={progress} variant="quantum" glow />
          
          <div className="flex items-center justify-center gap-4">
            <Button
              size="sm"
              variant="quantum"
              onClick={() => setIsPlaying(!isPlaying)}
            >
              {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
            </Button>
            <Button
              size="sm"
              variant="ghost"
              onClick={() => setProgress(0)}
            >
              <RotateCcw className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </Card>
      
      {/* Lesson List */}
      <div className="space-y-2">
        <h4 className="text-sm font-bold text-white/80">Available Lessons</h4>
        {lessons.map((lesson, index) => (
          <motion.div
            key={index}
            className={`p-3 rounded-lg border cursor-pointer transition-all ${
              index === currentLesson 
                ? 'border-neural-cyan/50 bg-neural-cyan/10' 
                : 'border-white/10 bg-white/5 hover:border-white/20'
            }`}
            onClick={() => setCurrentLesson(index)}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className={`w-2 h-2 rounded-full ${
                  lesson.completed ? 'bg-green-500' : 'bg-white/30'
                }`} />
                <span className="text-sm font-medium text-white/90">{lesson.title}</span>
              </div>
              <span className="text-xs text-white/60">{lesson.duration}</span>
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  )
}

// Echo Field Interface
function EchoFieldInterface({ nodeId }: { nodeId: string }) {
  const [message, setMessage] = useState('')
  const [messages, setMessages] = useState([
    {
      user: "Luna",
      message: "Just completed the quantum synchronization lesson! The patterns are becoming clearer.",
      timestamp: "2 min ago",
      type: "insight"
    },
    {
      user: "Nova",
      message: "Need help with DNA stabilization. Anyone have experience with stage 3 evolution?",
      timestamp: "5 min ago",
      type: "help"
    },
    {
      user: "Timeline AI",
      message: "Collective consciousness resonance at 87%. Beautiful harmony emerging.",
      timestamp: "8 min ago",
      type: "ai"
    }
  ])
  
  const sendMessage = () => {
    if (message.trim()) {
      setMessages(prev => [{
        user: "You",
        message: message.trim(),
        timestamp: "now",
        type: "insight"
      }, ...prev])
      setMessage('')
    }
  }
  
  return (
    <div className="space-y-6">
      <div className="text-center">
        <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-quantum-purple/20 flex items-center justify-center">
          <Users className="w-8 h-8 text-quantum-purple" />
        </div>
        <h3 className="text-xl font-orbitron font-bold text-quantum-purple mb-2">Echo Field</h3>
        <p className="text-sm text-white/60">Community wisdom & collaboration</p>
      </div>
      
      {/* Message Input */}
      <Card variant="quantum" className="p-4">
        <div className="flex gap-2">
          <input
            type="text"
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            placeholder="Share your insight or ask for help..."
            className="flex-1 bg-transparent border border-white/20 rounded px-3 py-2 text-sm text-white placeholder-white/50 focus:border-quantum-purple/50 focus:outline-none"
            onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
          />
          <Button size="sm" variant="quantum" onClick={sendMessage}>
            <Send className="w-4 h-4" />
          </Button>
        </div>
      </Card>
      
      {/* Messages Feed */}
      <div className="space-y-3 max-h-64 overflow-y-auto">
        {messages.map((msg, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className={`p-3 rounded-lg border-l-2 ${
              msg.type === 'ai' 
                ? 'border-neural-cyan bg-neural-cyan/5' 
                : msg.type === 'help'
                ? 'border-flame-orange bg-flame-orange/5'
                : 'border-quantum-purple bg-quantum-purple/5'
            }`}
          >
            <div className="flex items-center justify-between mb-1">
              <span className="text-sm font-medium text-white/90">{msg.user}</span>
              <span className="text-xs text-white/50">{msg.timestamp}</span>
            </div>
            <p className="text-sm text-white/80">{msg.message}</p>
          </motion.div>
        ))}
      </div>
    </div>
  )
}

// Meditation Interface
function MeditationInterface({ nodeId }: { nodeId: string }) {
  const [isActive, setIsActive] = useState(false)
  const [duration, setDuration] = useState(300) // 5 minutes
  const [timeLeft, setTimeLeft] = useState(duration)
  const [soundEnabled, setSoundEnabled] = useState(true)
  
  useEffect(() => {
    if (isActive && timeLeft > 0) {
      const interval = setInterval(() => {
        setTimeLeft(prev => prev - 1)
      }, 1000)
      
      return () => clearInterval(interval)
    } else if (timeLeft === 0) {
      setIsActive(false)
    }
  }, [isActive, timeLeft])
  
  const startMeditation = () => {
    setIsActive(true)
    setTimeLeft(duration)
  }
  
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }
  
  return (
    <div className="space-y-6">
      <div className="text-center">
        <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-green-500/20 flex items-center justify-center">
          <Heart className="w-8 h-8 text-green-500" />
        </div>
        <h3 className="text-xl font-orbitron font-bold text-green-500 mb-2">Meditation Space</h3>
        <p className="text-sm text-white/60">Balance & quantum stability</p>
      </div>
      
      {/* Meditation Timer */}
      <Card variant="quantum" className="p-6 text-center">
        <div className="text-4xl font-mono font-bold text-green-500 mb-4">
          {formatTime(timeLeft)}
        </div>
        
        <div className="mb-6">
          <Progress 
            value={((duration - timeLeft) / duration) * 100} 
            variant="quantum" 
            glow 
          />
        </div>
        
        <div className="flex items-center justify-center gap-4 mb-4">
          <Button
            variant={isActive ? "destructive" : "quantum"}
            onClick={() => isActive ? setIsActive(false) : startMeditation()}
          >
            {isActive ? "Stop" : "Begin Meditation"}
          </Button>
          
          <Button
            size="sm"
            variant="ghost"
            onClick={() => setSoundEnabled(!soundEnabled)}
          >
            {soundEnabled ? <Volume2 className="w-4 h-4" /> : <VolumeX className="w-4 h-4" />}
          </Button>
        </div>
        
        {/* Duration Selector */}
        <div className="flex justify-center gap-2">
          {[180, 300, 600, 900].map((dur) => (
            <Button
              key={dur}
              size="sm"
              variant={duration === dur ? "quantum" : "ghost"}
              onClick={() => setDuration(dur)}
              disabled={isActive}
            >
              {dur / 60}m
            </Button>
          ))}
        </div>
      </Card>
      
      {isActive && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center text-sm text-white/60"
        >
          <p>Focus on your breath...</p>
          <p>Feel the quantum field around you...</p>
          <p>Let consciousness expand...</p>
        </motion.div>
      )}
    </div>
  )
}

// TimelineGen Interface
function TimelineGenInterface({ nodeId }: { nodeId: string }) {
  const [selectedTool, setSelectedTool] = useState('matter')
  const [creationProgress, setCreationProgress] = useState(0)
  const [isCreating, setIsCreating] = useState(false)
  
  const tools = [
    { id: 'matter', name: 'Matter Synthesis', icon: Sparkles, color: 'text-flame-orange' },
    { id: 'energy', name: 'Energy Weaving', icon: Zap, color: 'text-neural-cyan' },
    { id: 'knowledge', name: 'Knowledge Crystallization', icon: BookOpen, color: 'text-quantum-purple' },
    { id: 'connection', name: 'Connection Forging', icon: MessageCircle, color: 'text-green-500' }
  ]
  
  const startCreation = () => {
    setIsCreating(true)
    setCreationProgress(0)
    
    const interval = setInterval(() => {
      setCreationProgress(prev => {
        if (prev >= 100) {
          setIsCreating(false)
          clearInterval(interval)
          return 100
        }
        return prev + 5
      })
    }, 200)
  }
  
  return (
    <div className="space-y-6">
      <div className="text-center">
        <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-flame-orange/20 flex items-center justify-center">
          <Cpu className="w-8 h-8 text-flame-orange" />
        </div>
        <h3 className="text-xl font-orbitron font-bold text-flame-orange mb-2">TimelineGen</h3>
        <p className="text-sm text-white/60">Creative matter production</p>
      </div>
      
      {/* Tool Selection */}
      <div className="grid grid-cols-2 gap-3">
        {tools.map((tool) => (
          <motion.div
            key={tool.id}
            className={`p-3 rounded-lg border cursor-pointer transition-all ${
              selectedTool === tool.id 
                ? 'border-flame-orange/50 bg-flame-orange/10' 
                : 'border-white/10 bg-white/5 hover:border-white/20'
            }`}
            onClick={() => setSelectedTool(tool.id)}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <div className="text-center">
              <tool.icon className={`w-6 h-6 mx-auto mb-2 ${tool.color}`} />
              <div className="text-xs font-medium text-white/90">{tool.name}</div>
            </div>
          </motion.div>
        ))}
      </div>
      
      {/* Creation Interface */}
      <Card variant="quantum" className="p-4">
        <h4 className="font-bold text-white mb-3">
          {tools.find(t => t.id === selectedTool)?.name}
        </h4>
        
        {creationProgress > 0 && (
          <div className="mb-4">
            <Progress value={creationProgress} variant="gaming" glow />
            <div className="text-xs text-white/60 mt-1 text-center">
              {isCreating ? 'Creating...' : 'Creation Complete!'}
            </div>
          </div>
        )}
        
        <div className="text-center">
          <Button
            variant="quantum"
            onClick={startCreation}
            disabled={isCreating}
            glow
          >
            {isCreating ? 'Creating...' : 'Begin Creation'}
          </Button>
        </div>
        
        {creationProgress === 100 && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            className="mt-4 p-3 bg-green-500/10 border border-green-500/30 rounded-lg text-center"
          >
            <Sparkles className="w-6 h-6 text-green-500 mx-auto mb-2" />
            <div className="text-sm text-green-400">
              +15 CE • +10 QS • New Timeline Element Created
            </div>
          </motion.div>
        )}
      </Card>
    </div>
  )
}

// Main Modal Component
export default function NodeInteractionModal({ nodeId, onClose }: NodeInteractionModalProps) {
  if (!nodeId) return null
  
  const nodeConfigs: Record<string, NodeConfig> = {
    'learning-node-1': { type: 'learning', title: 'Neural Learning', icon: Brain, color: 'text-neural-cyan', description: 'AI-curated consciousness expansion' },
    'learning-node-2': { type: 'learning', title: 'Advanced Learning', icon: Brain, color: 'text-neural-cyan', description: 'Advanced neural pathways' },
    'echo-field-1': { type: 'echo', title: 'Echo Field', icon: Users, color: 'text-quantum-purple', description: 'Community wisdom sharing' },
    'echo-field-2': { type: 'echo', title: 'Community Echo', icon: Users, color: 'text-quantum-purple', description: 'Collective insights' },
    'meditation-node-1': { type: 'meditation', title: 'Meditation Space', icon: Heart, color: 'text-green-500', description: 'Quantum balance & stability' },
    'timelinegen-1': { type: 'timelinegen', title: 'TimelineGen', icon: Cpu, color: 'text-flame-orange', description: 'Creative matter synthesis' }
  }
  
  const config = nodeConfigs[nodeId]
  if (!config) return null
  
  const renderInterface = () => {
    switch (config.type) {
      case 'learning':
        return <LearningNodeInterface nodeId={nodeId} />
      case 'echo':
        return <EchoFieldInterface nodeId={nodeId} />
      case 'meditation':
        return <MeditationInterface nodeId={nodeId} />
      case 'timelinegen':
        return <TimelineGenInterface nodeId={nodeId} />
      default:
        return <div>Unknown node type</div>
    }
  }
  
  return (
    <Dialog open={!!nodeId} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl bg-gradient-to-br from-space-dark via-black to-space-blue border border-neural-cyan/30">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <div className={`p-2 rounded-lg bg-gradient-to-br from-white/10 to-white/5 border border-white/20`}>
              <config.icon className={`w-6 h-6 ${config.color}`} />
            </div>
            {config.title}
          </DialogTitle>
          <DialogDescription>
            {config.description}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {renderInterface()}
        </div>
      </DialogContent>
    </Dialog>
  )
}
