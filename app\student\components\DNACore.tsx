'use client'

import { useState, useEffect, useRef, useMemo, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { OptimizedMotion } from '@/components/OptimizedMotion'
import { Canvas, useFrame } from '@react-three/fiber'
import { Sphere } from '@react-three/drei'
import * as THREE from 'three'
import {
  Dna,
  Zap,
  Brain,
  Activity,
  TrendingUp,
  Sparkles,
  Eye,
  Atom
} from 'lucide-react'

import { Progress } from '@/components/ui/progress'
import { Tooltip, TooltipContent, TooltipTrigger, TooltipProvider } from '@/components/ui/tooltip'
import { usePlayerData, useSystemStatus } from '../store/dashboardStore'

// Types
interface DNACoreProps {
  className?: string
}

interface DNAFragmentDisplayProps {
  fragment: string
  onHover?: (segment: string, meaning: string) => void
}

interface EnergyBarProps {
  type: 'CE' | 'QS'
  value: number
  lastActivity?: string
  className?: string
}

interface AvatarEvolutionOrbProps {
  stage: number
  ce: number
  qs: number
  streak: number
}

interface FluxFieldAnimationProps {
  ce: number
  qs: number
  stage: number
  particleCount?: number
}

interface MottoTextProps {
  text?: string
  visible?: boolean
}

// DNA Fragment Display Component
function DNAFragmentDisplay({ fragment, onHover }: DNAFragmentDisplayProps) {
  const [hoveredSegment, setHoveredSegment] = useState<string | null>(null)
  const [flickerStates, setFlickerStates] = useState<boolean[]>([])

  const segments = fragment.split('-')
  const segmentMeanings = {
    'AAAA': 'Biome Resonance: Quantum Field Attunement',
    'XXXX': 'Entropy Seed: Unique Consciousness Signature',
    'BBBB': 'Evolution Track: Learning Archetype Pattern',
    'ZZZZ': 'Timeline Cluster: Collaborative Network Link'
  }

  useEffect(() => {
    const interval = setInterval(() => {
      setFlickerStates(_prev =>
        segments.map(() => Math.random() > 0.7)
      )
    }, 150)

    return () => clearInterval(interval)
  }, [segments])

  return (
    <div className="text-center space-y-6">
      {/* DNA Fragment Title */}
      <div className="space-y-2">
        <h4 className="text-sm font-medium text-white/70 uppercase tracking-wider">
          DNA Signature
        </h4>
        <div className="w-16 h-px bg-gradient-to-r from-transparent via-neural-cyan to-transparent mx-auto" />
      </div>

      {/* DNA Segments Display */}
      <div className="flex items-center justify-center gap-4 px-4">
        {segments.map((segment, index) => (
          <TooltipProvider key={index}>
            <Tooltip>
              <TooltipTrigger asChild>
                <motion.div
                  className="relative group cursor-pointer"
                  onMouseEnter={() => {
                    setHoveredSegment(segment)
                    onHover?.(segment, segmentMeanings[segment as keyof typeof segmentMeanings] || 'Unknown segment')
                  }}
                  onMouseLeave={() => setHoveredSegment(null)}
                  whileHover={{ scale: 1.1 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  {/* Segment Background */}
                  <div className={`
                    px-4 py-3 rounded-xl border-2 transition-all duration-300
                    ${hoveredSegment === segment
                      ? 'border-flame-orange/60 bg-flame-orange/10'
                      : 'border-neural-cyan/30 bg-neural-cyan/5'
                    }
                    backdrop-blur-sm
                  `}>
                    <OptimizedMotion
                      className={`
                        text-xl font-mono font-bold transition-all duration-200 block
                        ${flickerStates[index] ? 'text-neural-cyan' : 'text-quantum-purple'}
                        ${hoveredSegment === segment ? 'text-flame-orange' : ''}
                        holographic-text-gaming
                      `}
                      data-text={segment}
                      animate={{
                        textShadow: flickerStates[index]
                          ? '0 0 15px #22D3EE, 0 0 30px #22D3EE'
                          : '0 0 8px #8B5CF6'
                      }}
                      quality="medium"
                      enableWillChange={true}
                    >
                      {segment}
                    </OptimizedMotion>
                  </div>

                  {/* Connecting Lines */}
                  {index < segments.length - 1 && (
                    <div className="absolute top-1/2 -right-2 transform -translate-y-1/2 w-4 h-px bg-gradient-to-r from-neural-cyan/50 to-quantum-purple/50" />
                  )}
                </motion.div>
              </TooltipTrigger>
              <TooltipContent className="max-w-xs p-4 bg-black/90 border-neural-cyan/40 text-white">
                <div className="space-y-2">
                  <div className="font-bold text-neural-cyan text-sm">{segment}</div>
                  <div className="text-white/80 text-xs leading-relaxed">
                    {segmentMeanings[segment as keyof typeof segmentMeanings] || 'Unknown segment'}
                  </div>
                </div>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        ))}
      </div>

      {/* DNA Sequence Info */}
      <div className="text-xs text-white/50 space-y-1">
        <div>Quantum-encoded consciousness pattern</div>
        <div className="flex items-center justify-center gap-2">
          <div className="w-1 h-1 bg-neural-cyan rounded-full animate-pulse" />
          <span>Real-time synchronization active</span>
          <div className="w-1 h-1 bg-neural-cyan rounded-full animate-pulse" style={{ animationDelay: '0.5s' }} />
        </div>
      </div>
    </div>
  )
}

// Energy Bar Component
function EnergyBar({ type, value, lastActivity, className }: EnergyBarProps) {
  const [isHovered, setIsHovered] = useState(false)

  const config = {
    CE: {
      label: 'Consciousness Energy',
      icon: Zap,
      color: 'text-neural-cyan',
      bgColor: 'from-neural-cyan/20 to-neural-cyan/5',
      borderColor: 'border-neural-cyan/40',
      variant: 'quantum' as const,
      gradient: 'from-neural-cyan to-cyan-400',
      glowColor: '#22D3EE'
    },
    QS: {
      label: 'Quantum Stability',
      icon: Brain,
      color: 'text-quantum-purple',
      bgColor: 'from-quantum-purple/20 to-quantum-purple/5',
      borderColor: 'border-quantum-purple/40',
      variant: 'gaming' as const,
      gradient: 'from-quantum-purple to-purple-400',
      glowColor: '#8B5CF6'
    }
  }

  const { label, icon: Icon, color, bgColor, borderColor, variant, gradient, glowColor } = config[type]

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <motion.div
            className={`
              relative p-5 rounded-xl cursor-pointer transition-all duration-300
              bg-gradient-to-br ${bgColor} backdrop-blur-sm
              border-2 ${borderColor} ${className}
            `}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
            whileHover={{ scale: 1.05, y: -2 }}
            animate={{
              boxShadow: isHovered
                ? `0 0 30px ${glowColor}40, 0 10px 20px rgba(0,0,0,0.3)`
                : '0 0 0px transparent'
            }}
          >
            {/* Header */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <motion.div
                  className={`p-2 rounded-lg bg-gradient-to-br ${bgColor} border ${borderColor}`}
                  animate={{ rotate: isHovered ? 360 : 0 }}
                  transition={{ duration: 0.6 }}
                >
                  <Icon className={`w-5 h-5 ${color}`} />
                </motion.div>
                <div>
                  <div className="text-sm font-medium text-white/90">{type}</div>
                  <div className="text-xs text-white/60">{label}</div>
                </div>
              </div>
            </div>

            {/* Value Display */}
            <div className="mb-4">
              <div className={`text-3xl font-bold ${color} mb-2 font-orbitron`}>
                {value}%
              </div>
              <div className="text-xs text-white/50">
                {value >= 80 ? 'Optimal' : value >= 60 ? 'Good' : value >= 40 ? 'Moderate' : 'Low'}
              </div>
            </div>

            {/* Progress Bar */}
            <div className="space-y-2">
              <Progress
                value={value}
                variant={variant}
                className="h-3 rounded-full"
                glow={isHovered}
              />

              {/* Additional visual indicator */}
              <motion.div
                className={`h-1 bg-gradient-to-r ${gradient} rounded-full opacity-60`}
                animate={{
                  scaleX: value / 100,
                  opacity: [0.4, 0.8, 0.4]
                }}
                transition={{
                  scaleX: { duration: 0.8, ease: "easeOut" },
                  opacity: { duration: 3, repeat: Infinity }
                }}
                style={{ transformOrigin: 'left' }}
              />
            </div>

            {/* Status Indicator */}
            <div className="mt-3 flex items-center gap-2">
              <motion.div
                className={`w-2 h-2 rounded-full bg-gradient-to-r ${gradient}`}
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
              />
              <span className="text-xs text-white/60">Live monitoring</span>
            </div>
          </motion.div>
        </TooltipTrigger>
        <TooltipContent className="p-4 bg-black/90 border-neural-cyan/40 text-white">
          <div className="space-y-2">
            <div className="font-bold text-sm">{label}</div>
            <div className="text-white/80 text-xs">Current Level: {value}%</div>
            {lastActivity && (
              <div className="text-white/60 text-xs border-t border-white/10 pt-2">
                Recent Activity: {lastActivity}
              </div>
            )}
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}

// Avatar Evolution Orb Component
function AvatarEvolutionOrb({ stage, ce, qs, streak: _streak }: AvatarEvolutionOrbProps) {
  const orbRef = useRef<THREE.Group>(null)
  const materialRef = useRef<THREE.ShaderMaterial>(null)

  // Evolution stage configurations
  const stageConfigs: Array<{ name: string; color: string; complexity: number; size: number }> = [
    { name: 'Static Nano-Core', color: '#666666', complexity: 1, size: 0.8 },
    { name: 'Liquidic Pulse Form', color: '#22D3EE', complexity: 2, size: 1.0 },
    { name: 'Fragmented Prism', color: '#8B5CF6', complexity: 3, size: 1.2 },
    { name: 'Self-aware Structure', color: '#F59E0B', complexity: 4, size: 1.4 },
    { name: 'Harmonic Resonator', color: '#10B981', complexity: 5, size: 1.6 },
    { name: 'Ascended Form', color: '#FFFFFF', complexity: 6, size: 2.0 }
  ]

  const currentStage = stageConfigs[Math.min(stage, stageConfigs.length - 1)] || stageConfigs[0]

  // Optimized shader material with performance improvements
  const shaderMaterial = useMemo(() => {
    return new THREE.ShaderMaterial({
      uniforms: {
        time: { value: 0 },
        energy: { value: (ce + qs) / 200 }, // Pre-computed energy
        color: { value: new THREE.Color(currentStage.color) },
        pulseFreq: { value: 2.0 }, // Reduced frequency
        distortionStrength: { value: 0.01 } // Reduced distortion
      },
      vertexShader: `
        varying vec2 vUv;
        varying float vDistFromCenter;
        varying float vPulse;
        uniform float time;
        uniform float energy;
        uniform float pulseFreq;
        uniform float distortionStrength;

        void main() {
          vUv = uv;

          // Pre-compute distance in vertex shader
          vec2 center = vec2(0.5);
          vDistFromCenter = distance(uv, center);

          // Pre-compute pulse in vertex shader
          vPulse = sin(time * pulseFreq + vDistFromCenter * 8.0) * 0.5 + 0.5;

          // Simplified vertex displacement
          vec3 pos = position;
          float displacement = sin(pos.x * 6.0 + time * 1.5) * distortionStrength * energy;
          pos += normal * displacement;

          gl_Position = projectionMatrix * modelViewMatrix * vec4(pos, 1.0);
        }
      `,
      fragmentShader: `
        uniform float energy;
        uniform vec3 color;
        varying vec2 vUv;
        varying float vDistFromCenter;
        varying float vPulse;

        void main() {
          // Use pre-computed values from vertex shader
          vec3 finalColor = color * (0.8 + energy * 0.4);

          // Simplified alpha calculation
          float alpha = (1.0 - vDistFromCenter * 1.8) * (0.6 + vPulse * 0.3 * energy);
          alpha = clamp(alpha, 0.0, 1.0);

          gl_FragColor = vec4(finalColor, alpha);
        }
      `,
      transparent: true,
      side: THREE.FrontSide // Changed from DoubleSide for performance
    })
  }, [currentStage.color, ce, qs])

  useFrame((state) => {
    if (!orbRef.current || !materialRef.current) return

    const time = state.clock.elapsedTime

    // Reduce update frequency even more for performance
    if (Math.floor(time * 5) % 4 !== 0) return

    // Update optimized shader uniforms
    materialRef.current.uniforms.time.value = time
    materialRef.current.uniforms.energy.value = (ce + qs) / 200 // Pre-computed energy

    // Slower rotation and floating animation
    orbRef.current.rotation.x = time * 0.1 // Further reduced
    orbRef.current.rotation.y = time * 0.15 // Further reduced
    orbRef.current.position.y = Math.sin(time * 1.2) * 0.03 // Further reduced amplitude

    // Simplified pulse effect with less frequent updates
    if (ce > 80 && Math.floor(time * 2) % 3 === 0) {
      const pulseScale = 1 + Math.sin(time * 3) * 0.03 // Further reduced
      orbRef.current.scale.setScalar(pulseScale * currentStage.size)
    } else if (Math.floor(time * 2) % 6 === 0) {
      orbRef.current.scale.setScalar(currentStage.size)
    }
  })

  return (
    <group ref={orbRef}>
      {/* Optimized Core Orb */}
      <Sphere args={[1, 16, 16]}> {/* Reduced from 32x32 to 16x16 */}
        <primitive object={shaderMaterial} ref={materialRef} />
      </Sphere>

      {/* Simplified Evolution-specific geometry */}
      {stage >= 2 && (
        <group>
          {Array.from({ length: Math.min((currentStage as any).complexity || 1, 4) }, (_, i) => (
            <Sphere
              key={i}
              position={[
                Math.cos((i / Math.min((currentStage as any).complexity || 1, 4)) * Math.PI * 2) * 1.2,
                Math.sin((i / Math.min((currentStage as any).complexity || 1, 4)) * Math.PI * 2) * 0.3,
                Math.sin((i / Math.min((currentStage as any).complexity || 1, 4)) * Math.PI * 2) * 1.2
              ]}
              args={[0.08, 6, 6]}
            >
              <meshBasicMaterial
                color={currentStage.color}
                transparent
                opacity={0.4} 
              />
            </Sphere>
          ))}
        </group>
      )}
    </group>
  )
}
// Flux Field Animation Component
function FluxFieldAnimation({ ce, qs, stage, particleCount = 100 }: FluxFieldAnimationProps) {
  const meshRef = useRef<THREE.InstancedMesh>(null)
  const positions = useRef<Float32Array | null>(null)
  const phases = useRef<Float32Array | null>(null)
  const velocities = useRef<Float32Array | null>(null)

  useEffect(() => {
    if (!meshRef.current) return

    const count = particleCount + (stage * 20)
    positions.current = new Float32Array(count * 3)
    phases.current = new Float32Array(count)
    velocities.current = new Float32Array(count * 3)

    for (let i = 0; i < count; i++) {
      // Random orbital parameters with different patterns per stage
      const radius = 2 + Math.random() * (2 + stage * 0.5)
      const theta = Math.random() * Math.PI * 2
      const phi = Math.random() * Math.PI

      positions.current[i * 3] = radius * Math.sin(phi) * Math.cos(theta)
      positions.current[i * 3 + 1] = radius * Math.sin(phi) * Math.sin(theta)
      positions.current[i * 3 + 2] = radius * Math.cos(phi)

      phases.current[i] = Math.random() * Math.PI * 2

      // Random velocities for orbital motion
      velocities.current[i * 3] = (Math.random() - 0.5) * 0.02
      velocities.current[i * 3 + 1] = (Math.random() - 0.5) * 0.02
      velocities.current[i * 3 + 2] = (Math.random() - 0.5) * 0.02
    }

    if (meshRef.current) {
      meshRef.current.geometry.setAttribute(
        'position',
        new THREE.BufferAttribute(positions.current, 3)
      )
      const positionAttribute = meshRef.current.geometry.attributes.position as THREE.BufferAttribute
      positionAttribute.setUsage(THREE.DynamicDrawUsage)
    }
  }, [particleCount, stage])

  useFrame((state) => {
    if (!meshRef.current || !positions.current || !phases.current || !velocities.current) return

    const time = state.clock.elapsedTime
    const matrix = new THREE.Matrix4()
    const count = Math.min(particleCount + (stage * 10), 60) // Reduced max particles

    // Update only every 3rd frame for performance
    const updateStep = 3
    const startIndex = Math.floor(time * 10) % updateStep

    for (let i = startIndex; i < count; i += updateStep) {
      const baseX = positions.current[i * 3]
      const baseY = positions.current[i * 3 + 1]
      const baseZ = positions.current[i * 3 + 2]

      // Simplified wavefield motion
      const waveX = baseX + Math.sin(time * 1.5 + phases.current[i]) * (ce / 100) * 0.4 // Reduced amplitude
      const waveY = baseY + Math.cos(time * 1.2 + phases.current[i]) * (qs / 100) * 0.3
      const waveZ = baseZ + Math.sin(time * 1.0 + phases.current[i]) * 0.2

      // Simplified orbital motion
      const orbitalX = waveX + velocities.current[i * 3] * time * 5 // Reduced speed
      const orbitalY = waveY + velocities.current[i * 3 + 1] * time * 5
      const orbitalZ = waveZ + velocities.current[i * 3 + 2] * time * 5

      // Simplified scale calculation
      const energyLevel = (ce + qs) / 300 // Reduced sensitivity
      const scale = 0.015 + (stage * 0.005) + Math.sin(time * 2 + phases.current[i]) * 0.005 * energyLevel

      matrix.setPosition(orbitalX, orbitalY, orbitalZ)
      matrix.scale(new THREE.Vector3(scale, scale, scale))
      meshRef.current.setMatrixAt(i, matrix)
    }

    meshRef.current.instanceMatrix.needsUpdate = true
  })

  // Dynamic color based on CE/QS ratio
  const particleColor = useMemo(() => {
    const ceRatio = ce / 100
    const qsRatio = qs / 100
    return new THREE.Color().setHSL(
      0.5 + (ceRatio - qsRatio) * 0.2, // Hue shifts based on energy balance
      0.8,
      0.4 + (ceRatio + qsRatio) * 0.3 // Brightness based on total energy
    )
  }, [ce, qs])

  return (
    <instancedMesh ref={meshRef} args={[undefined, undefined, particleCount + (stage * 20)]}>
      <sphereGeometry args={[1, 6, 6]} />
      <meshBasicMaterial
        color={particleColor}
        transparent
        opacity={0.7}
      />
    </instancedMesh>
  )
}

// Motto Text Component
function MottoText({ text = "Your DNA evolves as you evolve others.", visible = true }: MottoTextProps) {
  const [currentText, setCurrentText] = useState('')
  const [isTyping, setIsTyping] = useState(false)

  useEffect(() => {
    if (!visible) return

    setIsTyping(true)
    setCurrentText('')

    let index = 0
    const interval = setInterval(() => {
      if (index <= text.length) {
        setCurrentText(text.slice(0, index))
        index++
      } else {
        setIsTyping(false)
        clearInterval(interval)
      }
    }, 50)

    return () => clearInterval(interval)
  }, [text, visible])

  if (!visible) return null

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8, delay: 0.3 }}
      className="text-center space-y-4"
    >
      {/* Decorative elements */}
      <div className="flex items-center justify-center gap-4">
        <div className="w-8 h-px bg-gradient-to-r from-transparent to-neural-cyan/50" />
        <div className="w-2 h-2 border border-neural-cyan/50 rotate-45" />
        <div className="w-8 h-px bg-gradient-to-l from-transparent to-neural-cyan/50" />
      </div>

      {/* Motto text */}
      <div className="px-6">
        <motion.div
          className="text-sm text-white/80 italic font-light leading-relaxed"
          animate={{
            textShadow: [
              '0 0 10px rgba(34, 211, 238, 0.3)',
              '0 0 20px rgba(139, 92, 246, 0.4)',
              '0 0 10px rgba(34, 211, 238, 0.3)'
            ]
          }}
          transition={{ duration: 4, repeat: Infinity }}
        >
          &ldquo;{currentText}&rdquo;
          {isTyping && (
            <motion.span
              animate={{ opacity: [1, 0] }}
              transition={{ duration: 0.5, repeat: Infinity }}
              className="ml-1 text-neural-cyan"
            >
              |
            </motion.span>
          )}
        </motion.div>
      </div>

      {/* Attribution */}
      <div className="text-xs text-white/40 font-mono">
        — Quantum Consciousness Protocol
      </div>
    </motion.div>
  )
}
// Main DNA Core Module Component
export default function DNACore({ className }: DNACoreProps) {
  const playerData = usePlayerData()
  const systemStatus = useSystemStatus()
  const [hoveredSegment, setHoveredSegment] = useState<string | null>(null)
  const [segmentMeaning, setSegmentMeaning] = useState<string>('')
  const [_pulseIntensity, setPulseIntensity] = useState(1)
  const [lastCEActivity] = useState('Helped Luna in Logic Node')
  const [lastQSActivity] = useState('Meditation Session Complete')
  const [dnaFragment, setDnaFragment] = useState('NANO-ARCH-EVOL-SYNC')

  // Generate dynamic DNA fragment based on player data
  useEffect(() => {
    const generateDNAFragment = () => {
      const segments = [
        ['NANO', 'ARCH', 'EVOL', 'SYNC'],
        ['QNTM', 'FLUX', 'WAVE', 'CORE'],
        ['MIND', 'SOUL', 'BODY', 'SPRT'],
        ['LOVE', 'WISE', 'POWR', 'UNIT']
      ]
      const level = playerData?.level || 1
      const selectedSegments = segments.map((segment, index) =>
        segment[Math.min(Math.floor(level / 5) + index, segment.length - 1)]
      )
      setDnaFragment(selectedSegments.join('-'))
    }
    generateDNAFragment()
  }, [playerData?.level])

  // Calculate derived metrics
  const consciousnessEnergy = Math.round((systemStatus?.consciousnessLevel || 0) * 100)
  const quantumStability = Math.round((systemStatus?.systemHealth || 0) * 100)
  const evolutionStage = playerData?.level || 1
  const overallHealth = Math.round((consciousnessEnergy + quantumStability) / 2)
  const evolutionProgress = (evolutionStage / 20) * 100

  // Evolution stage names
  const stageNames = [
    'Static Nano-Core',
    'Liquidic Pulse Form',
    'Fragmented Prism',
    'Self-aware Structure',
    'Harmonic Resonator',
    'Ascended Form'
  ]

  // Optimized pulse effect with proper cleanup
  useEffect(() => {
    let counter = 0
    let animationFrame: number

    const updatePulse = () => {
      // Use deterministic values to avoid hydration issues
      setPulseIntensity(0.8 + ((counter * 0.1) % 0.4))
      counter++

      // Use setTimeout instead of setInterval for better performance
      setTimeout(() => {
        animationFrame = requestAnimationFrame(updatePulse)
      }, 2000)
    }

    animationFrame = requestAnimationFrame(updatePulse)

    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame)
      }
    }
  }, [])

  // Handle DNA segment hover with useCallback for performance
  const handleSegmentHover = useCallback((segment: string, meaning: string) => {
    setHoveredSegment(segment)
    setSegmentMeaning(meaning)
  }, [])

  // Debounced energy changes handler
  useEffect(() => {
    let timeoutId: NodeJS.Timeout

    if (consciousnessEnergy > 90) {
      timeoutId = setTimeout(() => {
        console.log('Consciousness Energy peaked! DNA Core resonating at maximum frequency.')
      }, 500) // Debounce for 500ms
    }

    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId)
      }
    }
  }, [consciousnessEnergy])

  return (
    <TooltipProvider>
      <OptimizedMotion
        className={`
          w-full h-full flex flex-col
          bg-gradient-to-br from-space-dark/95 via-space-dark/90 to-space-dark/95
          backdrop-blur-xl border border-neural-cyan/30 rounded-2xl
          shadow-2xl relative overflow-hidden ${className}
        `}
        preset="scaleIn"
        quality="medium"
        triggerOnView={true}
        enableWillChange={true}
      >
        {/* Header Section */}
        <div className="flex items-center justify-between p-6 border-b border-neural-cyan/20 bg-gradient-to-r from-neural-cyan/5 to-quantum-purple/5">
          <div className="flex items-center gap-3">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
              className="p-2 rounded-lg bg-neural-cyan/10 border border-neural-cyan/30"
            >
              <Dna className="w-6 h-6 text-neural-cyan" />
            </motion.div>
            <div>
              <h3 className="text-xl font-orbitron font-bold text-neural-cyan mb-1">DNA Core Module</h3>
              <p className="text-xs text-white/60">Consciousness Identity Matrix</p>
            </div>
          </div>
          <div className="flex items-center gap-3 text-sm text-white/70">
            <div className="flex items-center gap-2 px-3 py-1 rounded-full bg-neural-cyan/10 border border-neural-cyan/30">
              <Activity className="w-4 h-4 text-neural-cyan" />
              <span className="font-medium">Live</span>
              <motion.div
                className="w-2 h-2 bg-neural-cyan rounded-full"
                animate={{ scale: [1, 1.2, 1], opacity: [1, 0.7, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
              />
            </div>
          </div>
        </div>

        {/* DNA Fragment Display Section */}
        <div className="px-6 py-8 border-b border-neural-cyan/10">
          <DNAFragmentDisplay
            fragment={dnaFragment}
            onHover={handleSegmentHover}
          />

          {/* Motto Text with better spacing */}
          <div className="mt-6">
            <MottoText visible={true} />
          </div>
        </div>

        {/* Optimized 3D Visualization Area */}
        <div className="flex-1 relative min-h-[320px] p-4">
          <Canvas
            camera={{ position: [0, 0, 6], fov: 50 }}
            className="w-full h-full rounded-xl"
            gl={{
              antialias: false,
              alpha: true,
              powerPreference: 'low-power',
              stencil: false
            }}
            performance={{ min: 0.5 }}
            frameloop="demand"
            dpr={[1, 1.2]}
          >
            {/* Reduced lighting */}
            <ambientLight intensity={0.15} />
            <pointLight position={[10, 10, 10]} intensity={0.25} color="#22D3EE" />
            <pointLight position={[-10, -10, -10]} intensity={0.2} color="#8B5CF6" />
            {/* Removed third light for performance */}

            <AvatarEvolutionOrb
              stage={evolutionStage}
              ce={consciousnessEnergy}
              qs={quantumStability}
              streak={playerData?.stats.learningStreak || 0}
            />
            <FluxFieldAnimation
              ce={consciousnessEnergy}
              qs={quantumStability}
              stage={evolutionStage}
              particleCount={40} // Reduced from 80
            />
          </Canvas>

          {/* Floating HUD Elements with better positioning */}
          <div className="absolute inset-4 pointer-events-none">
            {/* Top Row - Energy Bars with proper spacing */}
            <div className="absolute top-0 left-0 space-y-4">
              <EnergyBar
                type="CE"
                value={consciousnessEnergy}
                lastActivity={lastCEActivity}
                className="w-32"
              />
              <EnergyBar
                type="QS"
                value={quantumStability}
                lastActivity={lastQSActivity}
                className="w-32"
              />
            </div>

            {/* Bottom Right - Evolution Status */}
            <div className="absolute bottom-0 right-0 space-y-3">
              <motion.div
                className="gaming-panel-inner p-4 rounded-xl bg-gradient-to-br from-flame-orange/10 to-flame-red/10 border border-flame-orange/30"
                whileHover={{ scale: 1.05 }}
              >
                <div className="flex items-center gap-2 mb-2">
                  <TrendingUp className="w-4 h-4 text-flame-orange" />
                  <span className="text-sm font-medium text-white/90">Evolution Stage</span>
                </div>
                <div className="text-lg font-bold text-flame-orange mb-2">
                  Level {evolutionStage}
                </div>
                <div className="text-xs text-flame-orange/80 mb-3">
                  {stageNames[Math.min(evolutionStage - 1, stageNames.length - 1)]}
                </div>
                <Progress value={evolutionProgress} variant="gaming" className="h-2" glow />
              </motion.div>

              <motion.div
                className="gaming-panel-inner p-4 rounded-xl bg-gradient-to-br from-neural-cyan/10 to-quantum-purple/10 border border-neural-cyan/30"
                whileHover={{ scale: 1.05 }}
              >
                <div className="flex items-center gap-2 mb-2">
                  <Sparkles className="w-4 h-4 text-neural-cyan" />
                  <span className="text-sm font-medium text-white/90">Overall Health</span>
                </div>
                <div className="text-2xl font-bold text-neural-cyan">{overallHealth}%</div>
              </motion.div>
            </div>

            {/* Center Info - Hovered Segment with better styling */}
            <AnimatePresence>
              {hoveredSegment && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.8, y: 20 }}
                  animate={{ opacity: 1, scale: 1, y: 0 }}
                  exit={{ opacity: 0, scale: 0.8, y: -20 }}
                  className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2
                           gaming-panel-inner p-6 rounded-xl max-w-sm text-center pointer-events-auto
                           bg-gradient-to-br from-space-dark/95 to-neural-cyan/10 border-2 border-neural-cyan/50"
                >
                  <div className="text-lg font-bold text-neural-cyan mb-3 font-orbitron">
                    {hoveredSegment}
                  </div>
                  <div className="text-sm text-white/80 leading-relaxed">
                    {segmentMeaning}
                  </div>
                  <div className="mt-4 w-full h-px bg-gradient-to-r from-transparent via-neural-cyan/50 to-transparent" />
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>

        {/* Enhanced Status Footer */}
        <div className="p-6 border-t border-neural-cyan/20 bg-gradient-to-r from-space-dark/80 via-neural-cyan/5 to-space-dark/80">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2 px-3 py-2 rounded-lg bg-neural-cyan/10 border border-neural-cyan/30">
                <Atom className="w-4 h-4 text-neural-cyan" />
                <div>
                  <div className="text-xs text-white/60">Quantum Coherence</div>
                  <div className="text-sm font-bold text-neural-cyan">{overallHealth}%</div>
                </div>
              </div>

              <div className="flex items-center gap-2 px-3 py-2 rounded-lg bg-quantum-purple/10 border border-quantum-purple/30">
                <Eye className="w-4 h-4 text-quantum-purple" />
                <div>
                  <div className="text-xs text-white/60">Active Streak</div>
                  <div className="text-sm font-bold text-quantum-purple">{playerData?.stats.learningStreak || 0} days</div>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-2 text-xs text-white/50">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
              <span>Neural sync active</span>
            </div>
          </div>
        </div>

        {/* Enhanced Quantum Glow Effect */}
        <motion.div
          className="absolute inset-0 pointer-events-none rounded-2xl"
          animate={{
            boxShadow: [
              '0 0 30px rgba(34, 211, 238, 0.2), 0 0 60px rgba(34, 211, 238, 0.1)',
              '0 0 50px rgba(139, 92, 246, 0.3), 0 0 80px rgba(139, 92, 246, 0.15)',
              '0 0 30px rgba(34, 211, 238, 0.2), 0 0 60px rgba(34, 211, 238, 0.1)'
            ]
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: 'easeInOut'
          }}
        />

        {/* Ambient particle effect overlay */}
        <div className="absolute inset-0 pointer-events-none rounded-2xl overflow-hidden">
          <div className="absolute top-0 left-0 w-full h-full opacity-30">
            {Array.from({ length: 8 }).map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-1 h-1 bg-neural-cyan rounded-full"
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                }}
                animate={{
                  y: [0, -20, 0],
                  opacity: [0, 1, 0],
                  scale: [0, 1, 0]
                }}
                transition={{
                  duration: 3 + Math.random() * 2,
                  repeat: Infinity,
                  delay: Math.random() * 2
                }}
              />
            ))}
          </div>
        </div>
      </OptimizedMotion>
    </TooltipProvider>
  )
}
