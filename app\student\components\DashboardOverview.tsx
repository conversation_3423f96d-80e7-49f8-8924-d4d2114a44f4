"use client"

import { motion } from "framer-motion"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { ChevronRight, Flame, Trophy, Users } from "lucide-react"
import { StatCard, DailyQuest, Achievement, RecentActivity, QuickAction } from "../types/dashboard"

interface DashboardOverviewProps {
  stats: StatCard[]
  dailyQuests: DailyQuest[]
  achievements: Achievement[]
  recentActivity: RecentActivity[]
  quickActions: QuickAction[]
}

export function DashboardOverview({
  stats,
  dailyQuests,
  achievements,
  recentActivity,
  quickActions
}: DashboardOverviewProps) {
  return (
    <motion.div
      key="dashboard"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="space-y-6"
    >
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: index * 0.1 }}
            whileHover={{ scale: 1.05 }}
          >
            <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl hover:bg-black/60 transition-all duration-300">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-gray-400 text-sm">{stat.title}</p>
                    <p className="text-2xl font-bold text-white">{stat.value}</p>
                  </div>
                  <div className={`p-3 rounded-lg bg-gradient-to-r ${stat.color}`}>
                    <stat.icon className="w-6 h-6 text-white" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Daily Quests */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-cyan-400">
                <Flame className="w-5 h-5" />
                Daily Quests
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {dailyQuests.map((quest, index) => (
                <motion.div
                  key={quest.title}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 + index * 0.1 }}
                  className="p-4 bg-gray-800/30 rounded-lg border border-gray-700/50"
                >
                  <div className="flex justify-between items-start mb-2">
                    <h4 className="font-medium text-white">{quest.title}</h4>
                    <Badge className="bg-gradient-to-r from-cyan-500 to-blue-500 text-white">
                      +{quest.xp} XP
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    <Progress value={(quest.progress / quest.total) * 100} className="flex-1 h-2 bg-gray-700">
                      <div
                        className="h-full bg-gradient-to-r from-cyan-500 to-blue-500 rounded-full transition-all duration-500"
                        style={{ width: `${(quest.progress / quest.total) * 100}%` }}
                      />
                    </Progress>
                    <span className="text-sm text-gray-400">
                      {quest.progress}/{quest.total}
                    </span>
                  </div>
                </motion.div>
              ))}
            </CardContent>
          </Card>
        </motion.div>

        {/* Achievements */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-yellow-400">
                <Trophy className="w-5 h-5" />
                Achievements
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-3">
                {achievements.map((achievement, index) => (
                  <motion.div
                    key={achievement.name}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 0.4 + index * 0.1 }}
                    whileHover={{ scale: 1.05 }}
                    className={`p-3 rounded-lg border text-center transition-all duration-300 ${
                      achievement.unlocked
                        ? "bg-gradient-to-r from-yellow-500/20 to-amber-500/20 border-yellow-500/30 shadow-lg shadow-yellow-500/20"
                        : "bg-gray-800/30 border-gray-700/50 opacity-50"
                    }`}
                  >
                    <achievement.icon
                      className={`w-6 h-6 mx-auto mb-2 ${
                        achievement.unlocked ? "text-yellow-400" : "text-gray-500"
                      }`}
                    />
                    <p
                      className={`text-xs font-medium ${
                        achievement.unlocked ? "text-white" : "text-gray-500"
                      }`}
                    >
                      {achievement.name}
                    </p>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Recent Activity */}
      <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.4 }}>
        <Card className="bg-black/40 border-gray-800/50 backdrop-blur-xl">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-purple-400">
              <Users className="w-5 h-5" />
              Community Activity
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {recentActivity.map((activity, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.5 + index * 0.1 }}
                  className="flex items-center gap-3 p-3 bg-gray-800/30 rounded-lg border border-gray-700/50 hover:bg-gray-800/50 transition-all duration-300"
                >
                  <Avatar className="w-8 h-8">
                    <AvatarFallback className="bg-gradient-to-r from-purple-500 to-pink-500 text-xs">
                      {activity.user.slice(0, 2)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <p className="text-sm">
                      <span className="font-medium text-purple-400">{activity.user}</span>
                      <span className="text-gray-400"> {activity.action} </span>
                      <span className="text-white">{activity.item}</span>
                    </p>
                    <p className="text-xs text-gray-500">{activity.time}</p>
                  </div>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Quick Actions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
        className="grid grid-cols-1 md:grid-cols-3 gap-6"
      >
        {quickActions.map((action, index) => (
          <motion.div key={action.title} whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
            <Card
              className="bg-black/40 border-gray-800/50 backdrop-blur-xl hover:bg-black/60 transition-all duration-300 cursor-pointer group"
              onClick={action.action}
            >
              <CardContent className="p-6 text-center">
                <div
                  className={`w-12 h-12 mx-auto mb-4 rounded-lg bg-gradient-to-r ${action.color} flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}
                >
                  <action.icon className="w-6 h-6 text-white" />
                </div>
                <h3 className="font-semibold text-white mb-2">{action.title}</h3>
                <p className="text-gray-400 text-sm">{action.desc}</p>
                <ChevronRight className="w-4 h-4 mx-auto mt-2 text-gray-500 group-hover:text-cyan-400 transition-colors duration-300" />
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </motion.div>
    </motion.div>
  )
}
