'use client'

import { motion } from 'framer-motion'
import { EvolutionTheme } from './types'

interface SidebarEffectsProps {
  evolutionTheme: EvolutionTheme
}

export function SidebarEffects({ evolutionTheme }: SidebarEffectsProps) {
  return (
    <>
      {/* Enhanced Quantum Aura Effect */}
      <motion.div
        className="absolute inset-0 pointer-events-none rounded-r-2xl"
        style={{
          background: `linear-gradient(135deg, ${evolutionTheme.primary}05, transparent, ${evolutionTheme.secondary}05)`,
          border: `1px solid ${evolutionTheme.primary}20`
        }}
        animate={{
          boxShadow: [
            `0 0 30px ${evolutionTheme.glow}, inset 0 0 30px ${evolutionTheme.primary}10`,
            `0 0 50px ${evolutionTheme.glow}, inset 0 0 50px ${evolutionTheme.primary}15`,
            `0 0 30px ${evolutionTheme.glow}, inset 0 0 30px ${evolutionTheme.primary}10`
          ]
        }}
        transition={{
          duration: 4,
          repeat: Infinity,
          ease: 'easeInOut'
        }}
      />

      {/* Quantum Evolution Indicator */}
      <div className="absolute top-6 right-6 pointer-events-none">
        <motion.div className="relative">
          {/* Core orb */}
          <motion.div
            className="w-4 h-4 rounded-full relative z-10"
            style={{
              background: `radial-gradient(circle, ${evolutionTheme.primary}, ${evolutionTheme.secondary})`,
              boxShadow: `0 0 15px ${evolutionTheme.glow}`
            }}
            animate={{
              scale: [1, 1.3, 1],
              opacity: [0.8, 1, 0.8]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: 'easeInOut'
            }}
          />

          {/* Orbiting particles */}
          {Array.from({ length: 3 }).map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 rounded-full"
              style={{
                backgroundColor: evolutionTheme.primary,
                top: '50%',
                left: '50%',
                transformOrigin: '0 0'
              }}
              animate={{
                rotate: [0, 360],
                scale: [0.5, 1, 0.5]
              }}
              transition={{
                rotate: { duration: 3 + i, repeat: Infinity, ease: "linear" },
                scale: { duration: 2, repeat: Infinity, ease: "easeInOut", delay: i * 0.5 }
              }}
              initial={{
                x: 15 + i * 5,
                y: -2
              }}
            />
          ))}
        </motion.div>
      </div>

      {/* Quantum Field Lines */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden rounded-r-2xl">
        {Array.from({ length: 4 }).map((_, i) => (
          <motion.div
            key={i}
            className="absolute h-0.5 opacity-20"
            style={{
              background: `linear-gradient(90deg, transparent, ${evolutionTheme.primary}, transparent)`,
              width: '100%',
              top: `${20 + i * 20}%`
            }}
            animate={{
              x: ['-100%', '100%'],
              opacity: [0, 0.3, 0]
            }}
            transition={{
              duration: 4 + i,
              repeat: Infinity,
              delay: i * 0.5,
              ease: "easeInOut"
            }}
          />
        ))}
      </div>
    </>
  )
}
