'use client'

import { useState, useEffect } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Users,
  Settings,
  Heart,
  LogOut,
  ChevronLeft,
  ChevronRight,
  Dna,
  GraduationCap,
  FlaskConical,
  Award,
  Gamepad2,
  Bot,
  Hexagon,
  MessageCircle
} from 'lucide-react'

import { Button } from '@/components/ui/button'
import { usePlayerData, useTimelineData, useDashboardStore } from '../store/dashboardStore'

interface SidebarProps {
  className?: string
  onCollapseChange?: (collapsed: boolean) => void
}

interface MenuItem {
  id: string
  label: string
  icon: any
  badge?: string | number
  route?: string
  action?: () => void
}

export default function Sidebar({ className, onCollapseChange }: SidebarProps) {
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [activeItem, setActiveItem] = useState('dashboard')

  const [compactMode, setCompactMode] = useState(false)
  const [showCommandPalette, setShowCommandPalette] = useState(false)

  const router = useRouter()
  const pathname = usePathname()
  const playerData = usePlayerData()
  const timelineData = useTimelineData()
  const { setActivePanel } = useDashboardStore()

  // Update active item based on current pathname
  useEffect(() => {
    const currentPath = pathname.split('/').pop() || 'dashboard'
    setActiveItem(currentPath)
  }, [pathname])

  // Evolution stage color themes based on player level
  const getEvolutionTheme = () => {
    const stage = playerData?.level || 1
    if (stage <= 2) return {
      primary: 'rgba(34, 211, 238, 1)', // Neural cyan
      secondary: 'rgba(139, 92, 246, 1)', // Quantum purple
      glow: 'rgba(34, 211, 238, 0.3)'
    }
    if (stage <= 5) return {
      primary: 'rgba(139, 92, 246, 1)', // Quantum purple
      secondary: 'rgba(245, 158, 11, 1)', // Flame orange
      glow: 'rgba(139, 92, 246, 0.3)'
    }
    if (stage <= 8) return {
      primary: 'rgba(245, 158, 11, 1)', // Flame orange
      secondary: 'rgba(239, 68, 68, 1)', // Red
      glow: 'rgba(245, 158, 11, 0.3)'
    }
    return {
      primary: 'rgba(255, 215, 0, 1)', // Gold
      secondary: 'rgba(255, 255, 255, 1)', // White
      glow: 'rgba(255, 215, 0, 0.4)'
    }
  }

  const evolutionTheme = getEvolutionTheme()

  // Hotkey handling
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Command palette toggle (Ctrl/Cmd + K)
      if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
        event.preventDefault()
        setShowCommandPalette(prev => !prev)
      }

      // Quick navigation hotkeys (Ctrl/Cmd + number)
      if ((event.ctrlKey || event.metaKey) && event.key >= '1' && event.key <= '9') {
        event.preventDefault()
        const index = parseInt(event.key) - 1
        const menuItem = menuItems[index]
        if (menuItem) {
          setActiveItem(menuItem.id)
          if (menuItem.route) {
            router.push(menuItem.route)
          } else if (menuItem.action) {
            menuItem.action()
          }
        }
      }

      // Compact mode toggle (Ctrl/Cmd + Shift + C)
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'C') {
        event.preventDefault()
        setCompactMode(prev => !prev)
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [])

  const menuItems: MenuItem[] = [
    {
      id: 'dashboard',
      label: 'NanoCore',
      icon: Dna,
      badge: `${playerData?.level || 0}`,
      route: '/dashboard',
      action: () => {
        setActiveItem('dashboard')
        setActivePanel('dashboard-overview')
      }
    },
    // {
    //   id: 'timeline-map',
    //   label: 'Timeline Map',
    //   icon: Compass,
    //   badge: `${timelineData?.;availableNodes.length || 0}`,
    //   action: () => setViewMode('explore')
    // },
    // {
    //   id: 'evolution',
    //   label: 'NanoEvolution',
    //   icon: GraduationCap,
    //   badge: 'AI',
    //   route: '/dashboard/evolution'
    // },
    // {
    //   id: 'mind-balance',
    //   label: 'Mind & Balance',
    //   icon: Heart,
    //   badge: `${Math.round((playerData?.xp || 0) / 100)}%`,
    //   route: '/dashboard/mind-balance'
    // },
    // {
    //   id: 'nanoverse',
    //   label: 'NanoVerse',
    //   icon: Hexagon,
    //   badge: '3D',
    //   route: '/dashboard/nanoverse'
    // },
    // {
    //   id: 'nanolab',
    //   label: 'NanoLab',
    //   icon: FlaskConical,
    //   badge: '🧪',
    //   route: '/dashboard/nanolab'
    // },
    // {
    //   id: 'quantumlink',
    //   label: 'QuantumLink',
    //   icon: MessageCircle,
    //   badge: 'Chat',
    //   route: '/dashboard/quantumlink'
    // },
    // {
    //   id: 'achievements',
    //   label: 'Achievements',
    //   icon: Award,
    //   badge: `${playerData?.stats.learningStreak || 0}d`,
    //   route: '/dashboard/achievements'
    // },
    // {
    //   id: 'playgrounds',
    //   label: 'Playgrounds',
    //   icon: Gamepad2,
    //   badge: 'Sim',
    //   route: '/dashboard/playgrounds'
    // },
    // {
    //   id: 'timeline-cohort',
    //   label: 'Timeline Cohort',
    //   icon: Users,
    //   badge: `${timelineData?.nodes.length || 0}`,
    //   route: '/dashboard/timeline-cohort'
    // },
    // {
    //   id: 'ai-mentor',
    //   label: 'AI Mentor',
    //   icon: Bot,
    //   badge: 'Active',
    //   route: '/dashboard/ai-mentor'
    // }
  ]

  const bottomMenuItems: MenuItem[] = [
    // {
    //   id: 'settings',
    //   label: 'Settings & Account',
    //   icon: Settings,
    //   route: '/dashboard/settings'
    // },
    // {
    //   id: 'logout',
    //   label: 'Disconnect',
    //   icon: LogOut,
    //   action: () => console.log('Logout')
    // }
  ]

  const handleItemClick = (item: MenuItem) => {
    setActiveItem(item.id)
    if (item.route) {
      router.push(item.route)
    } else if (item.action) {
      item.action()
    }
  }



  // Enhanced quantum menu item styles
  const getMenuItemStyle = (isActive: boolean, isHovered: boolean = false) => ({
    width: '100%',
    display: 'flex',
    alignItems: 'center',
    gap: '0.75rem',
    padding: '1rem',
    borderRadius: '1rem',
    transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
    position: 'relative' as const,
    overflow: 'hidden' as const,
    border: '2px solid',
    backdropFilter: 'blur(16px)',
    background: isActive
      ? `linear-gradient(135deg, ${evolutionTheme.primary}25, ${evolutionTheme.secondary}15, ${evolutionTheme.primary}25)`
      : isHovered
      ? 'linear-gradient(135deg, rgba(34, 211, 238, 0.08), rgba(139, 92, 246, 0.08), rgba(34, 211, 238, 0.08))'
      : 'linear-gradient(135deg, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.2))',
    borderColor: isActive
      ? `${evolutionTheme.primary}80`
      : isHovered
      ? `${evolutionTheme.primary}40`
      : 'rgba(255, 255, 255, 0.1)',
    boxShadow: isActive
      ? `
        0 8px 32px ${evolutionTheme.glow},
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        0 0 20px ${evolutionTheme.primary}40
      `
      : isHovered
      ? `
        0 4px 20px rgba(34, 211, 238, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1)
      `
      : 'inset 0 1px 0 rgba(255, 255, 255, 0.05)',
    color: isActive ? evolutionTheme.primary : 'rgba(255, 255, 255, 0.8)',
    transform: isHovered ? 'scale(1.02) translateY(-1px)' : 'scale(1)',
  })



  const getBottomMenuItemStyle = (isActive: boolean, isLogout: boolean, isHovered: boolean = false) => ({
    width: '100%',
    display: 'flex',
    alignItems: 'center',
    gap: '0.75rem',
    padding: '0.75rem 1rem',
    borderRadius: '1rem',
    transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
    position: 'relative' as const,
    overflow: 'hidden' as const,
    border: '2px solid',
    backdropFilter: 'blur(16px)',
    background: isActive
      ? 'linear-gradient(135deg, rgba(34, 211, 238, 0.25), rgba(139, 92, 246, 0.15), rgba(34, 211, 238, 0.25))'
      : isLogout && isHovered
      ? 'linear-gradient(135deg, rgba(239, 68, 68, 0.15), rgba(248, 113, 113, 0.2), rgba(239, 68, 68, 0.15))'
      : isHovered
      ? 'linear-gradient(135deg, rgba(34, 211, 238, 0.08), rgba(139, 92, 246, 0.08), rgba(34, 211, 238, 0.08))'
      : 'linear-gradient(135deg, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.2))',
    borderColor: isActive
      ? 'rgba(34, 211, 238, 0.6)'
      : isLogout && isHovered
      ? 'rgba(239, 68, 68, 0.5)'
      : isHovered
      ? 'rgba(34, 211, 238, 0.3)'
      : 'rgba(255, 255, 255, 0.1)',
    boxShadow: isActive
      ? `
        0 8px 32px rgba(34, 211, 238, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        0 0 20px rgba(34, 211, 238, 0.4)
      `
      : isLogout && isHovered
      ? `
        0 4px 20px rgba(239, 68, 68, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1)
      `
      : isHovered
      ? `
        0 4px 20px rgba(34, 211, 238, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1)
      `
      : 'inset 0 1px 0 rgba(255, 255, 255, 0.05)',
    color: isActive
      ? 'rgba(34, 211, 238, 1)'
      : isLogout && isHovered
      ? 'rgba(239, 68, 68, 1)'
      : isLogout
      ? 'rgba(239, 68, 68, 0.8)'
      : 'rgba(255, 255, 255, 0.8)',
    transform: isHovered ? 'scale(1.02) translateY(-1px)' : 'scale(1)',
  })

  const getIconStyle = (isActive: boolean, isLogout: boolean = false, isHovered: boolean = false) => ({
    width: '1.25rem',
    height: '1.25rem',
    flexShrink: 0,
    position: 'relative' as const,
    zIndex: 10,
    transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
    filter: isActive
      ? `drop-shadow(0 0 8px ${evolutionTheme.primary}) drop-shadow(0 2px 4px ${evolutionTheme.primary}80)`
      : isLogout && isHovered
      ? 'drop-shadow(0 0 6px rgba(239, 68, 68, 0.8)) drop-shadow(0 2px 4px rgba(239, 68, 68, 0.5))'
      : isHovered
      ? 'drop-shadow(0 0 6px rgba(34, 211, 238, 0.6)) drop-shadow(0 2px 4px rgba(34, 211, 238, 0.3))'
      : 'none',
    transform: isActive ? 'scale(1.1)' : isHovered ? 'scale(1.05)' : 'scale(1)',
  })



  return (
    <>
      <motion.div
        className={`fixed left-0 top-0 h-full quantum-glass border-r-2 z-40 ${className}`}
        style={{
          background: 'linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(10, 15, 28, 0.9) 50%, rgba(0, 0, 0, 0.8) 100%)',
          backdropFilter: 'blur(20px)',
          borderRightColor: `${evolutionTheme.primary}40`,
          boxShadow: `
            inset 0 1px 0 rgba(255, 255, 255, 0.1),
            0 0 30px ${evolutionTheme.glow},
            0 8px 32px rgba(0, 0, 0, 0.4)
          `
        }}
        animate={{
          width: isCollapsed || compactMode ? '80px' : '280px',
          borderRightColor: `${evolutionTheme.primary}40`
        }}
        transition={{ duration: 0.3, ease: 'easeInOut' }}
      >
      {/* Quantum Header */}
      <div
        className="p-4 border-b-2 relative overflow-hidden"
        style={{
          borderBottomColor: `${evolutionTheme.primary}30`,
          background: 'linear-gradient(135deg, rgba(34, 211, 238, 0.05) 0%, rgba(139, 92, 246, 0.05) 100%)'
        }}
      >


        <div className="flex items-center justify-between relative z-10">
          <AnimatePresence>
            {!isCollapsed && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                className="flex items-center gap-3"
              >

                <Hexagon
                  className="w-8 h-8 text-neural-cyan"
                  style={{
                    filter: `drop-shadow(0 0 10px ${evolutionTheme.primary})`
                  }}
                />

                <div className='flex flex-col space-y-1 p-2'>
                  <motion.h3
                    className="text-lg font-orbitron font-bold"
                    style={{
                      background: `linear-gradient(45deg, ${evolutionTheme.primary}, ${evolutionTheme.secondary})`,
                      WebkitBackgroundClip: 'text',
                      WebkitTextFillColor: 'transparent',
                      backgroundClip: 'text',
                      textShadow: `0 0 20px ${evolutionTheme.glow}`
                    }}
                    animate={{
                      textShadow: [
                        `0 0 20px ${evolutionTheme.glow}`,
                        `0 0 30px ${evolutionTheme.glow}`,
                        `0 0 20px ${evolutionTheme.glow}`
                      ]
                    }}
                    transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                  >
                    NanoHero
                  </motion.h3>
                  
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          <div className="flex gap-2">

            <Button
              size="sm"
              variant="quantum"
              onClick={() => {
                const newCollapsed = !isCollapsed
                setIsCollapsed(newCollapsed)
                onCollapseChange?.(newCollapsed)
              }}
              className="p-2 relative overflow-hidden group"
              style={{
                background: `linear-gradient(45deg, ${evolutionTheme.secondary}80, ${evolutionTheme.primary}80)`,
                border: `2px solid ${evolutionTheme.secondary}60`,
                boxShadow: `0 0 15px ${evolutionTheme.glow}`
              }}
            >
              <motion.div
                animate={{ rotate: isCollapsed ? 180 : 0 }}
                transition={{ duration: 0.3 }}
              >
                {isCollapsed ? <ChevronRight className="w-4 h-4 relative z-10" /> : <ChevronLeft className="w-4 h-4 relative z-10" />}
              </motion.div>

            </Button>
          </div>
        </div>
      </div>

      {/* Navigation Menu */}
      <div className="flex-1 overflow-y-auto py-6">
        <nav className="space-y-3 px-4">
          {menuItems.map((item) => (
            <div key={item.id}>
              {/* Main Menu Item */}
              <motion.button
                style={getMenuItemStyle(activeItem === item.id || activeItem.startsWith(`${item.id}-`))}
                onClick={() => handleItemClick(item)}
                whileHover={{
                  scale: 1.02,
                  boxShadow: activeItem === item.id || activeItem.startsWith(`${item.id}-`)
                    ? "0 8px 32px rgba(34, 211, 238, 0.3)"
                    : "0 4px 20px rgba(255, 255, 255, 0.1)"
                }}
                whileTap={{ scale: 0.98 }}
                onMouseEnter={(e) => {
                  const isActive = activeItem === item.id || activeItem.startsWith(`${item.id}-`);
                  Object.assign(e.currentTarget.style, getMenuItemStyle(isActive, true));
                }}
                onMouseLeave={(e) => {
                  const isActive = activeItem === item.id || activeItem.startsWith(`${item.id}-`);
                  Object.assign(e.currentTarget.style, getMenuItemStyle(isActive, false));
                }}
              >
                {/* Enhanced Quantum Effects */}
                <div className="absolute inset-0 pointer-events-none">
                  {/* Quantum sweep effect */}
                  <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                    <div
                      className="absolute inset-0 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"
                      style={{
                        background: `linear-gradient(90deg, transparent, ${evolutionTheme.primary}30, transparent)`
                      }}
                    />
                  </div>

                  {/* Active quantum field */}
                  {(activeItem === item.id || activeItem.startsWith(`${item.id}-`)) && (
                    <>
                      <motion.div
                        className="absolute inset-0"
                        style={{
                          background: `linear-gradient(45deg, ${evolutionTheme.primary}08, ${evolutionTheme.secondary}08, ${evolutionTheme.primary}08)`
                        }}
                        animate={{
                          opacity: [0.3, 0.6, 0.3]
                        }}
                        transition={{
                          duration: 2,
                          repeat: Infinity,
                          ease: "easeInOut"
                        }}
                      />

                      {/* Quantum particles */}
                      {Array.from({ length: 3 }).map((_, i) => (
                        <motion.div
                          key={i}
                          className="absolute w-1 h-1 rounded-full"
                          style={{
                            backgroundColor: evolutionTheme.primary,
                            left: `${20 + i * 30}%`,
                            top: '50%'
                          }}
                          animate={{
                            x: [0, 50, 0],
                            y: [0, -10, 0],
                            opacity: [0, 1, 0],
                            scale: [0.5, 1, 0.5]
                          }}
                          transition={{
                            duration: 2 + i * 0.5,
                            repeat: Infinity,
                            delay: i * 0.3,
                            ease: "easeInOut"
                          }}
                        />
                      ))}
                    </>
                  )}
                </div>

                <item.icon
                  style={getIconStyle(
                    activeItem === item.id || activeItem.startsWith(`${item.id}-`),
                    false,
                    false
                  )}
                />
                
                <AnimatePresence>
                  {!isCollapsed && (
                    <motion.div
                      initial={{ opacity: 0, width: 0 }}
                      animate={{ opacity: 1, width: 'auto' }}
                      exit={{ opacity: 0, width: 0 }}
                      className="flex items-center justify-between flex-1 overflow-hidden"
                    >
                      <span className="text-sm font-medium truncate">{item.label}</span>
                      <div className="flex items-center gap-2">
                        {item.badge && (
                          <motion.span
                            className={`text-xs px-2 py-1 rounded-full font-medium ${
                              typeof item.badge === 'string' && item.badge === 'Live'
                                ? 'bg-green-500/20 text-green-400 border border-green-400/30'
                                : typeof item.badge === 'string' && item.badge === 'AI'
                                ? 'bg-purple-500/20 text-purple-400 border border-purple-400/30'
                                : typeof item.badge === 'string' && item.badge === 'Build'
                                ? 'bg-orange-500/20 text-orange-400 border border-orange-400/30'
                                : typeof item.badge === 'string' && item.badge === 'Sim'
                                ? 'bg-blue-500/20 text-blue-400 border border-blue-400/30'
                                : typeof item.badge === 'string' && item.badge === 'Active'
                                ? 'bg-cyan-500/20 text-cyan-400 border border-cyan-400/30'
                                : `bg-gradient-to-r from-${evolutionTheme.primary}20 to-${evolutionTheme.secondary}20 border border-${evolutionTheme.primary}30`
                            }`}
                            style={{
                              color: typeof item.badge === 'number' || (typeof item.badge === 'string' && /^\d+/.test(item.badge))
                                ? evolutionTheme.primary
                                : undefined
                            }}
                            animate={{
                              scale: typeof item.badge === 'string' && item.badge === 'Live' ? [1, 1.1, 1] : 1
                            }}
                            transition={{
                              duration: 2,
                              repeat: typeof item.badge === 'string' && item.badge === 'Live' ? Infinity : 0,
                              ease: 'easeInOut'
                            }}
                          >
                            {item.badge}
                          </motion.span>
                        )}

                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.button>


            </div>
          ))}
        </nav>
      </div>

      {/* Quantum Bottom Menu */}
      <div
        className="border-t-2 p-4 space-y-3 relative overflow-hidden"
        style={{
          borderTopColor: `${evolutionTheme.primary}30`,
          background: 'linear-gradient(135deg, rgba(34, 211, 238, 0.03) 0%, rgba(139, 92, 246, 0.03) 100%)'
        }}
      >
        {/* Quantum field lines */}
        {/* <div className="absolute inset-0 pointer-events-none">
          <div
            className="absolute top-0 left-0 w-full h-0.5 opacity-30"
            style={{
              background: `linear-gradient(90deg, transparent, ${evolutionTheme.primary}, transparent)`
            }}
          />
        </div> */}
        {bottomMenuItems.map((item) => (
          <motion.button
            key={item.id}
            style={getBottomMenuItemStyle(
              activeItem === item.id,
              item.id === 'logout'
            )}
            onClick={() => handleItemClick(item)}
            whileHover={{
              scale: 1.02,
              boxShadow: activeItem === item.id
                ? "0 8px 32px rgba(34, 211, 238, 0.3)"
                : item.id === 'logout'
                ? "0 4px 20px rgba(239, 68, 68, 0.2)"
                : "0 4px 20px rgba(255, 255, 255, 0.1)"
            }}
            whileTap={{ scale: 0.98 }}
            onMouseEnter={(e) => {
              const isActive = activeItem === item.id;
              const isLogout = item.id === 'logout';
              Object.assign(e.currentTarget.style, getBottomMenuItemStyle(isActive, isLogout, true));
            }}
            onMouseLeave={(e) => {
              const isActive = activeItem === item.id;
              const isLogout = item.id === 'logout';
              Object.assign(e.currentTarget.style, getBottomMenuItemStyle(isActive, isLogout, false));
            }}
          >
            {/* Enhanced Bottom Menu Quantum Effects */}
            <div className="absolute inset-0 pointer-events-none">
              {/* Quantum sweep effect */}
              <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                <div
                  className="absolute inset-0 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"
                  style={{
                    background: item.id === 'logout'
                      ? 'linear-gradient(90deg, transparent, rgba(239, 68, 68, 0.3), transparent)'
                      : `linear-gradient(90deg, transparent, ${evolutionTheme.primary}30, transparent)`
                  }}
                />
              </div>

              {/* Active quantum field */}
              {activeItem === item.id && (
                <motion.div
                  className="absolute inset-0"
                  style={{
                    background: `linear-gradient(45deg, ${evolutionTheme.primary}08, ${evolutionTheme.secondary}08, ${evolutionTheme.primary}08)`
                  }}
                  animate={{
                    opacity: [0.3, 0.6, 0.3]
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                />
              )}
            </div>

            <item.icon
              style={getIconStyle(
                activeItem === item.id,
                item.id === 'logout',
                false
              )}
            />
            
            <AnimatePresence>
              {!isCollapsed && (
                <motion.span
                  initial={{ opacity: 0, width: 0 }}
                  animate={{ opacity: 1, width: 'auto' }}
                  exit={{ opacity: 0, width: 0 }}
                  style={{
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                    position: 'relative',
                    zIndex: 10,
                    transition: 'all 0.3s ease',
                  }}
                >
                  {item.label}
                </motion.span>
              )}
            </AnimatePresence>
          </motion.button>
        ))}
      </div>

      {/* Enhanced Quantum Aura Effect */}
      <motion.div
        className="absolute inset-0 pointer-events-none rounded-r-2xl"
        style={{
          background: `linear-gradient(135deg, ${evolutionTheme.primary}05, transparent, ${evolutionTheme.secondary}05)`,
          border: `1px solid ${evolutionTheme.primary}20`
        }}
        animate={{
          boxShadow: [
            `0 0 30px ${evolutionTheme.glow}, inset 0 0 30px ${evolutionTheme.primary}10`,
            `0 0 50px ${evolutionTheme.glow}, inset 0 0 50px ${evolutionTheme.primary}15`,
            `0 0 30px ${evolutionTheme.glow}, inset 0 0 30px ${evolutionTheme.primary}10`
          ]
        }}
        transition={{
          duration: 4,
          repeat: Infinity,
          ease: 'easeInOut'
        }}
      />

      {/* Quantum Evolution Indicator */}
      <div className="absolute top-6 right-6 pointer-events-none">
        <motion.div className="relative">
          {/* Core orb */}
          <motion.div
            className="w-4 h-4 rounded-full relative z-10"
            style={{
              background: `radial-gradient(circle, ${evolutionTheme.primary}, ${evolutionTheme.secondary})`,
              boxShadow: `0 0 15px ${evolutionTheme.glow}`
            }}
            animate={{
              scale: [1, 1.3, 1],
              opacity: [0.8, 1, 0.8]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: 'easeInOut'
            }}
          />

          {/* Orbiting particles */}
          {Array.from({ length: 3 }).map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 rounded-full"
              style={{
                backgroundColor: evolutionTheme.primary,
                top: '50%',
                left: '50%',
                transformOrigin: '0 0'
              }}
              animate={{
                rotate: [0, 360],
                scale: [0.5, 1, 0.5]
              }}
              transition={{
                rotate: { duration: 3 + i, repeat: Infinity, ease: "linear" },
                scale: { duration: 2, repeat: Infinity, ease: "easeInOut", delay: i * 0.5 }
              }}
              initial={{
                x: 15 + i * 5,
                y: -2
              }}
            />
          ))}
        </motion.div>
      </div>

      {/* Quantum Field Lines */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden rounded-r-2xl">
        {Array.from({ length: 4 }).map((_, i) => (
          <motion.div
            key={i}
            className="absolute h-0.5 opacity-20"
            style={{
              background: `linear-gradient(90deg, transparent, ${evolutionTheme.primary}, transparent)`,
              width: '100%',
              top: `${20 + i * 20}%`
            }}
            animate={{
              x: ['-100%', '100%'],
              opacity: [0, 0.3, 0]
            }}
            transition={{
              duration: 4 + i,
              repeat: Infinity,
              delay: i * 0.5,
              ease: "easeInOut"
            }}
          />
        ))}
      </div>
    </motion.div>
    </>
  )
}
