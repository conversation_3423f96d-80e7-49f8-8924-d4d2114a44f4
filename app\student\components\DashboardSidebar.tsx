"use client"

import React, { memo, useMemo, useCallback } from "react"
import { motion } from "framer-motion"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Progress } from "@/components/ui/progress"
import Image from "next/image"
import { MenuItem } from "../types/dashboard"

interface DashboardSidebarProps {
  activeSection: string
  setActiveSection: (section: string) => void
  userLevel: number
  userXP: number
  menuItems: MenuItem[]
}

export const DashboardSidebar = memo(function DashboardSidebar({
  activeSection,
  setActiveSection,
  userLevel,
  userXP,
  menuItems
}: DashboardSidebarProps) {
  // Memoize XP progress calculation
  const xpProgress = useMemo(() => {
    const xpForCurrentLevel = userLevel * 1000
    const xpForNextLevel = (userLevel + 1) * 1000
    return ((userXP - xpForCurrentLevel) / (xpForNextLevel - xpForCurrentLevel)) * 100
  }, [userLevel, userXP])

  // Memoize menu item click handler
  const handleMenuClick = useCallback((section: string) => {
    setActiveSection(section)
  }, [setActiveSection])
  return (
    <motion.div
      initial={{ x: -300 }}
      animate={{ x: 0 }}
      className="w-80 bg-black/50 backdrop-blur-xl border-r border-gray-800/50 p-6"
    >
      {/* Logo */}
      <motion.div className="flex items-center gap-3 mb-8" whileHover={{ scale: 1.05 }}>
        <Image src="/logo.png" alt="NanoHero Logo" width={260} height={30} />
      </motion.div>

      {/* User Profile */}
      <motion.div
        className="bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-4 mb-6 border border-gray-700/50"
        whileHover={{ scale: 1.02 }}
      >
        <div className="flex items-center gap-3 mb-3">
          <Avatar className="w-12 h-12 border-2 border-cyan-500/50">
            <AvatarImage src="/placeholder.svg?height=48&width=48" />
            <AvatarFallback className="bg-gradient-to-r from-cyan-500 to-blue-500">CE</AvatarFallback>
          </Avatar>
          <div>
            <h3 className="font-semibold text-white">CyberExplorer</h3>
            <p className="text-sm text-gray-400">Level {userLevel}</p>
            <p className="text-xs text-cyan-400">{userXP} XP</p>
          </div>
        </div>
      </motion.div>

      {/* XP Progress */}
      <div className="bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-4 mb-6 border border-gray-700/50">
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>XP Progress</span>
            <span className="text-cyan-400">{userXP}/3000</span>
          </div>
          <Progress value={(userXP / 3000) * 100} className="h-2 bg-gray-700">
            <div
              className="h-full bg-gradient-to-r from-cyan-500 to-blue-500 rounded-full transition-all duration-500"
              style={{ width: `${(userXP / 3000) * 100}%` }}
            />
          </Progress>
        </div>
      </div>

      {/* Navigation */}
      <nav className="space-y-2">
        {menuItems.map((item, index) => (
          <motion.button
            key={item.id}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: index * 0.1 }}
            onClick={() => setActiveSection(item.id)}
            className={`w-full flex items-center gap-3 p-3 rounded-lg transition-all duration-300 group ${
              activeSection === item.id
                ? "bg-gradient-to-r from-cyan-500/20 to-blue-500/20 border border-cyan-500/30 shadow-lg shadow-cyan-500/20"
                : "hover:bg-gray-800/50"
            }`}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <div
              className={`p-2 rounded-lg bg-gradient-to-r ${item.color} ${
                activeSection === item.id ? "shadow-lg" : "opacity-70 group-hover:opacity-100"
              }`}
            >
              <item.icon className="w-4 h-4 text-white" />
            </div>
            <span
              className={`font-medium ${
                activeSection === item.id ? "text-cyan-400" : "text-gray-300 group-hover:text-white"
              }`}
            >
              {item.label}
            </span>
            {activeSection === item.id && (
              <motion.div layoutId="activeIndicator" className="ml-auto w-2 h-2 bg-cyan-400 rounded-full" />
            )}
          </motion.button>
        ))}
      </nav>
    </motion.div>
  )
})
