"use client"

import { motion, AnimatePresence } from 'framer-motion'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  <PERSON>, 
  <PERSON>, 
  Sparkles, 
  Heart, 
  Atom, 
  Play, 
  MessageCircle,
  Lightbulb,
  Target,
  Zap
} from 'lucide-react'

interface NanoNode {
  id: string
  type: 'learning' | 'echo' | 'meditation' | 'timelinegen'
  title: string
  description: string
  status: 'active' | 'dormant' | 'completed'
  position: { x: number; y: number; z: number }
  color: string
  participants: number
  lastActivity: string
  completionRate: number
  rewards: { ce: number; qs: number }
}

interface EnhancedNodeModalProps {
  node: NanoNode | null
  isOpen: boolean
  onClose: () => void
  onEnterNode: (node: NanoNode) => void
}

function LearningNodeContent({ node }: { node: NanoNode }) {
  return (
    <div className="space-y-4">
      <div className="p-4 bg-cyan-500/10 border border-cyan-500/30 rounded-lg">
        <h4 className="text-cyan-400 font-bold mb-2 flex items-center gap-2">
          <Brain className="w-4 h-4" />
          AI-Curated Learning Experience
        </h4>
        <p className="text-white/80 text-sm mb-3">
          Interactive neural pathway enhancement through adaptive challenges and real-time feedback.
        </p>
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-white/60">Difficulty Level</span>
            <span className="text-cyan-400">Adaptive</span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-white/60">Estimated Duration</span>
            <span className="text-cyan-400">15-30 min</span>
          </div>
        </div>
      </div>
      
      <div className="grid grid-cols-2 gap-3">
        <Button className="bg-cyan-500/20 text-cyan-400 border border-cyan-500/30">
          <Play className="w-4 h-4 mr-2" />
          Start Learning
        </Button>
        <Button variant="outline" className="border-gray-600 text-gray-400">
          <Lightbulb className="w-4 h-4 mr-2" />
          Preview
        </Button>
      </div>
    </div>
  )
}

function EchoNodeContent({ node }: { node: NanoNode }) {
  return (
    <div className="space-y-4">
      <div className="p-4 bg-purple-500/10 border border-purple-500/30 rounded-lg">
        <h4 className="text-purple-400 font-bold mb-2 flex items-center gap-2">
          <Sparkles className="w-4 h-4" />
          Community Wisdom Exchange
        </h4>
        <p className="text-white/80 text-sm mb-3">
          Share insights, ask questions, and contribute to the collective knowledge of your Timeline.
        </p>
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-white/60">Active Discussions</span>
            <span className="text-purple-400">7</span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-white/60">Wisdom Points Available</span>
            <span className="text-purple-400">+{node.rewards.ce} CE</span>
          </div>
        </div>
      </div>
      
      <div className="grid grid-cols-2 gap-3">
        <Button className="bg-purple-500/20 text-purple-400 border border-purple-500/30">
          <MessageCircle className="w-4 h-4 mr-2" />
          Join Echo
        </Button>
        <Button variant="outline" className="border-gray-600 text-gray-400">
          <Sparkles className="w-4 h-4 mr-2" />
          Browse Wisdom
        </Button>
      </div>
    </div>
  )
}

function MeditationNodeContent({ node }: { node: NanoNode }) {
  return (
    <div className="space-y-4">
      <div className="p-4 bg-green-500/10 border border-green-500/30 rounded-lg">
        <h4 className="text-green-400 font-bold mb-2 flex items-center gap-2">
          <Heart className="w-4 h-4" />
          Quantum Balance Experience
        </h4>
        <p className="text-white/80 text-sm mb-3">
          Guided meditation with biofeedback integration to stabilize consciousness energy and enhance quantum coherence.
        </p>
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-white/60">Session Type</span>
            <span className="text-green-400">Breath & Visual</span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-white/60">QS Boost Potential</span>
            <span className="text-green-400">+{node.rewards.qs} QS</span>
          </div>
        </div>
      </div>
      
      <div className="grid grid-cols-2 gap-3">
        <Button className="bg-green-500/20 text-green-400 border border-green-500/30">
          <Heart className="w-4 h-4 mr-2" />
          Begin Meditation
        </Button>
        <Button variant="outline" className="border-gray-600 text-gray-400">
          <Target className="w-4 h-4 mr-2" />
          Set Intention
        </Button>
      </div>
    </div>
  )
}

function TimelineGenContent({ node }: { node: NanoNode }) {
  return (
    <div className="space-y-4">
      <div className="p-4 bg-red-500/10 border border-red-500/30 rounded-lg">
        <h4 className="text-red-400 font-bold mb-2 flex items-center gap-2">
          <Atom className="w-4 h-4" />
          Matter Synthesis Workshop
        </h4>
        <p className="text-white/80 text-sm mb-3">
          Construct quantum materials and devices using consciousness energy. Collaborate with others to build Timeline infrastructure.
        </p>
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-white/60">Available Materials</span>
            <span className="text-red-400">Quantum, Neural, Crystalline</span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-white/60">CE Investment</span>
            <span className="text-red-400">25-100 CE</span>
          </div>
        </div>
      </div>
      
      <div className="grid grid-cols-2 gap-3">
        <Button className="bg-red-500/20 text-red-400 border border-red-500/30">
          <Atom className="w-4 h-4 mr-2" />
          Enter Workshop
        </Button>
        <Button variant="outline" className="border-gray-600 text-gray-400">
          <Zap className="w-4 h-4 mr-2" />
          View Blueprints
        </Button>
      </div>
    </div>
  )
}

export default function EnhancedNodeModal({
  node,
  isOpen,
  onClose,
  onEnterNode
}: EnhancedNodeModalProps) {
  if (!node) return null

  const getNodeIcon = () => {
    switch (node.type) {
      case 'learning': return <Brain className="w-5 h-5" />
      case 'echo': return <Sparkles className="w-5 h-5" />
      case 'meditation': return <Heart className="w-5 h-5" />
      case 'timelinegen': return <Atom className="w-5 h-5" />
    }
  }

  const getNodeContent = () => {
    switch (node.type) {
      case 'learning': return <LearningNodeContent node={node} />
      case 'echo': return <EchoNodeContent node={node} />
      case 'meditation': return <MeditationNodeContent node={node} />
      case 'timelinegen': return <TimelineGenContent node={node} />
    }
  }

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          onClick={onClose}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className="bg-black/90 border border-gray-700 rounded-xl p-6 max-w-lg w-full max-h-[80vh] overflow-y-auto"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-3">
                <div
                  className="p-2 rounded-lg"
                  style={{ backgroundColor: node.color + '20' }}
                >
                  {getNodeIcon()}
                </div>
                <div>
                  <h3 className="text-xl font-bold text-white">{node.title}</h3>
                  <p className="text-white/60 text-sm capitalize">{node.type} Node</p>
                </div>
              </div>
              <Button
                size="sm"
                variant="outline"
                onClick={onClose}
                className="border-gray-600 text-gray-400"
              >
                ✕
              </Button>
            </div>
            
            {/* Node Status */}
            <div className="flex items-center gap-4 mb-6 text-sm">
              <div className="flex items-center gap-2">
                <div
                  className="w-2 h-2 rounded-full"
                  style={{ backgroundColor: node.color }}
                />
                <span className="text-white/60 capitalize">{node.status}</span>
              </div>
              <div className="flex items-center gap-1">
                <Users className="w-4 h-4 text-gray-400" />
                <span className="text-white/60">{node.participants} active</span>
              </div>
              <div className="text-white/60">{node.lastActivity}</div>
            </div>
            
            {/* Description */}
            <p className="text-white/80 mb-6">{node.description}</p>
            
            {/* Progress */}
            <div className="mb-6">
              <div className="flex justify-between items-center mb-2">
                <span className="text-white/60 text-sm">Completion Rate</span>
                <span className="text-white/80 text-sm">{node.completionRate}%</span>
              </div>
              <Progress value={node.completionRate} className="h-2" />
            </div>
            
            {/* Rewards */}
            <div className="flex gap-2 mb-6">
              <Badge className="bg-cyan-500/20 text-cyan-400 border-cyan-500/30">
                +{node.rewards.ce} CE
              </Badge>
              <Badge className="bg-purple-500/20 text-purple-400 border-purple-500/30">
                +{node.rewards.qs} QS
              </Badge>
            </div>
            
            {/* Node-specific content */}
            {getNodeContent()}
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}
