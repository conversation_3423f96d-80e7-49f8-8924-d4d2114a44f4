"use client"

import { motion } from "framer-motion"
import { <PERSON>, Bell, Settings } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

interface DashboardHeaderProps {
  activeSection: string
  notifications: number
  onLogout: () => void
}

export function DashboardHeader({ activeSection, notifications, onLogout }: DashboardHeaderProps) {
  const getHeaderContent = () => {
    switch (activeSection) {
      case "learn":
        return {
          title: "Tutorial Zone",
          subtitle: "Discover, learn, and share knowledge"
        }
      case "education":
        return {
          title: "Life Skills Adventure",
          subtitle: "Master life skills through interactive comic adventures"
        }
      case "gamer":
        return {
          title: "Gamer Zone",
          subtitle: "Share, compete, and dominate the leaderboards"
        }
      case "hack":
        return {
          title: "Hack Lab",
          subtitle: "Learn cybersecurity through safe, gamified challenges"
        }
      case "creator":
        return {
          title: "Creator Studio",
          subtitle: "Express yourself, share your journey, and inspire others"
        }
      case "community":
        return {
          title: "Community Lounge",
          subtitle: "Connect, learn, and grow together in our safe community space"
        }
      case "epochtowns":
        return {
          title: "Epoch Towns",
          subtitle: "Collaborate, evolve, and build your legacy with your monthly cohort"
        }
      default:
        return {
          title: "Welcome back, CyberExplorer!",
          subtitle: "Ready to level up your skills today?"
        }
    }
  }

  const { title, subtitle } = getHeaderContent()

  return (
    <motion.header
      initial={{ y: -50, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      className="bg-black/30 backdrop-blur-xl border-b border-gray-800/50 p-6"
    >
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
            {title}
          </h2>
          <p className="text-gray-400">{subtitle}</p>
        </div>
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" className="bg-black/50 border-gray-700 hover:bg-gray-800">
            <Search className="w-4 h-4 mr-2" />
            Search
          </Button>
          <Button variant="outline" size="sm" className="bg-black/50 border-gray-700 hover:bg-gray-800 relative">
            <Bell className="w-4 h-4" />
            {notifications > 0 && (
              <Badge className="absolute -top-2 -right-2 w-5 h-5 p-0 bg-red-500 text-xs">{notifications}</Badge>
            )}
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="bg-black/50 border-gray-700 hover:bg-gray-800"
            onClick={onLogout}
          >
            Sign Out
          </Button>
          <Button variant="outline" size="sm" className="bg-black/50 border-gray-700 hover:bg-gray-800">
            <Settings className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </motion.header>
  )
}
