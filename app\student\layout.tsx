"use client"

import React, { useState } from "react"

// Force dynamic rendering for dashboard
export const dynamic = 'force-dynamic'
import { motion } from "framer-motion"
import { useDashboardStore } from "./store/dashboardStore"
import { ChatProvider } from "@/contexts/ChatContext"
// import { QuantumLinkPanel } from "@/components/chat/QuantumLinkPanel"
import Sidebar from "./components/Sidebar"
import TopNav from "./components/TopNav"
import { ClientOnlyWrapper, ClientOnlyLoadingFallback } from '@/components/ClientOnlyWrapper'

interface DashboardLayoutProps {
  children: React.ReactNode
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [chatPanelOpen, setChatPanelOpen] = useState(false)
  const { viewMode } = useDashboardStore()

  return (
    <ClientOnlyWrapper fallback={<ClientOnlyLoadingFallback />}>
      <ChatProvider>
      <div className={`min-h-screen bg-space-gradient relative overflow-hidden transition-all duration-1000 ${
        viewMode === 'focus' ? 'bg-opacity-90' :
        viewMode === 'assist' ? 'bg-gradient-to-br from-space-dark via-green-900/10 to-space-dark' :
        viewMode === 'syntropy' ? 'bg-gradient-to-br from-space-dark via-flame-orange/5 to-space-dark' :
        ''
      }`}>
      {/* Sidebar Navigation */}
      <Sidebar onCollapseChange={setSidebarCollapsed} />

      {/* Top Navigation */}
      <TopNav sidebarCollapsed={sidebarCollapsed} />

      {/* Enhanced Quantum Background */}
      {/* <QuantumBackground /> */}

      {/* Dynamic Background Effects */}
      <div className="absolute inset-0 pointer-events-none">
        {/* Explore Mode - Default quantum effects */}
        {viewMode === 'explore' && (
          <div className="absolute inset-0">
            <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-neural-cyan/10 rounded-full blur-3xl animate-pulse" />
            <div className="absolute bottom-1/3 right-1/4 w-28 h-28 bg-quantum-purple/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }} />
          </div>
        )}

        {/* Focus Mode - Concentrated energy */}
        {viewMode === 'focus' && (
          <div className="absolute inset-0">
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-48 h-48 bg-neural-cyan/15 rounded-full blur-3xl animate-pulse" />
          </div>
        )}

        {/* Assist Mode - Collaborative energy */}
        {viewMode === 'assist' && (
          <div className="absolute inset-0">
            <div className="absolute top-1/3 left-1/3 w-36 h-36 bg-green-400/10 rounded-full blur-3xl animate-pulse" />
            <div className="absolute bottom-1/4 right-1/3 w-32 h-32 bg-green-400/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1.2s' }} />
          </div>
        )}

        {/* Syntropy Mode - Creative energy */}
        {viewMode === 'syntropy' && (
          <div className="absolute inset-0">
            <div className="absolute top-1/3 left-1/3 w-40 h-40 bg-flame-orange/10 rounded-full blur-3xl animate-pulse" />
            <div className="absolute bottom-1/4 right-1/3 w-36 h-36 bg-flame-orange/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1.5s' }} />
          </div>
        )}
      </div>

        {/* Main Content Area */}
        <div
          className="relative z-20 transition-all duration-300"
          style={{
            marginLeft: sidebarCollapsed ? '80px' : '280px',
            marginTop: '64px',
            height: 'calc(100vh - 64px)',
            width: chatPanelOpen
              ? sidebarCollapsed ? 'calc(100% - 80px - 384px)' : 'calc(100% - 280px - 384px)'
              : sidebarCollapsed ? 'calc(100% - 80px)' : 'calc(100% - 280px)'
          }}
        >
          <div className="h-full overflow-y-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="p-6"
            >
              {children}
            </motion.div>
          </div>
        </div>

        {/* QuantumLink Chat Panel */}
        {/* <QuantumLinkPanel
          isOpen={chatPanelOpen}
          onToggle={() => setChatPanelOpen(!chatPanelOpen)}
        /> */}
      </div>
    </ChatProvider>
    </ClientOnlyWrapper>
  )
}
