"use client"

import React, { useState } from "react"

// Force dynamic rendering for dashboard
export const dynamic = 'force-dynamic'
import { motion } from "framer-motion"
import { useDashboardStore } from "./store/dashboardStore"
import { AuthProvider } from "@/contexts/AuthContext"
import { Cha<PERSON><PERSON><PERSON>ider } from "@/contexts/ChatContext"
// import { QuantumLinkPanel } from "@/components/chat/QuantumLinkPanel"
import Sidebar from "./components/Sidebar"
import TopNav from "./components/TopNav"
import { ClientOnlyWrapper, ClientOnlyLoadingFallback } from '@/components/ClientOnlyWrapper'

interface DashboardLayoutProps {
  children: React.ReactNode
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [chatPanelOpen, setChatPanelOpen] = useState(false)
  const { viewMode } = useDashboardStore()

  return (
    <ClientOnlyWrapper fallback={<ClientOnlyLoadingFallback />}>
      <AuthProvider>
        <ChatProvider>
      <div className={`min-h-screen relative overflow-hidden transition-all duration-1000 ${
        viewMode === 'focus' ? 'bg-gradient-to-br from-space-dark via-neural-cyan/10 to-space-blue' :
        viewMode === 'assist' ? 'bg-gradient-to-br from-space-dark via-green-500/10 to-space-teal' :
        viewMode === 'syntropy' ? 'bg-gradient-to-br from-space-dark via-quantum-gold/10 to-space-blue' :
        'bg-gradient-to-br from-space-dark via-quantum-purple/8 to-space-blue'
      }`}>

      {/* Enhanced Quantum Background Layer */}
      <div className="absolute inset-0 bg-space-gradient opacity-90" />

      {/* Consciousness Wave Overlay */}
      <div className="absolute inset-0 consciousness-wave opacity-20" />

      {/* Quantum Shimmer Effect */}
      <div className="absolute inset-0 quantum-shimmer opacity-30" />
      {/* Sidebar Navigation */}
      <Sidebar onCollapseChange={setSidebarCollapsed} />

      {/* Top Navigation */}
      <TopNav sidebarCollapsed={sidebarCollapsed} />

      {/* Enhanced Quantum Background */}
      {/* <QuantumBackground /> */}

      {/* Enhanced Dynamic Background Effects */}
      <div className="absolute inset-0 pointer-events-none">
        {/* Explore Mode - Default quantum effects */}
        {/* {viewMode === 'explore' && (
          <div className="absolute inset-0">
            <div className="absolute top-1/4 left-1/4 w-40 h-40 bg-neural-cyan/15 rounded-full blur-3xl animate-pulse quantum-glow" />
            <div className="absolute bottom-1/3 right-1/4 w-36 h-36 bg-quantum-purple/15 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }} />
            <div className="absolute top-2/3 left-1/2 w-32 h-32 bg-quantum-gold/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }} /> */}
            {/* Floating particles */}
            {/* <div className="absolute top-1/2 right-1/3 w-2 h-2 bg-neural-cyan rounded-full animate-bounce" style={{ animationDelay: '0.5s' }} />
            <div className="absolute bottom-1/2 left-2/3 w-1.5 h-1.5 bg-quantum-purple rounded-full animate-bounce" style={{ animationDelay: '1.5s' }} /> */}
          {/* </div>
        )} */}

        {/* Focus Mode - Concentrated energy */}
        {viewMode === 'focus' && (
          <div className="absolute inset-0">
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-56 h-56 bg-neural-cyan/20 rounded-full blur-3xl animate-pulse quantum-glow" />
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-white/5 rounded-full blur-2xl animate-pulse" style={{ animationDelay: '0.5s' }} />
            {/* Concentric rings */}
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-72 h-72 border border-neural-cyan/20 rounded-full animate-ping" />
          </div>
        )}

        {/* Assist Mode - Collaborative energy */}
        {viewMode === 'assist' && (
          <div className="absolute inset-0">
            <div className="absolute top-1/3 left-1/3 w-44 h-44 bg-green-400/15 rounded-full blur-3xl animate-pulse" />
            <div className="absolute bottom-1/4 right-1/3 w-40 h-40 bg-emerald-500/15 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1.2s' }} />
            <div className="absolute top-1/2 right-1/4 w-36 h-36 bg-teal-400/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2.4s' }} />
            {/* Connection lines effect */}
            <div className="absolute top-1/3 left-1/3 w-1 h-32 bg-gradient-to-b from-green-400/30 to-transparent rotate-45" />
            <div className="absolute bottom-1/4 right-1/3 w-1 h-28 bg-gradient-to-t from-emerald-500/30 to-transparent -rotate-45" />
          </div>
        )}

        {/* Syntropy Mode - Creative energy */}
        {viewMode === 'syntropy' && (
          <div className="absolute inset-0">
            <div className="absolute top-1/3 left-1/3 w-48 h-48 bg-quantum-gold/15 rounded-full blur-3xl animate-pulse" />
            <div className="absolute bottom-1/4 right-1/3 w-44 h-44 bg-orange-500/15 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1.5s' }} />
            <div className="absolute top-1/2 left-1/2 w-40 h-40 bg-yellow-400/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '3s' }} />
            {/* Creative sparks */}
            <div className="absolute top-1/4 right-1/4 w-1 h-1 bg-quantum-gold rounded-full animate-ping" style={{ animationDelay: '0.8s' }} />
            <div className="absolute bottom-1/3 left-1/4 w-1.5 h-1.5 bg-orange-400 rounded-full animate-ping" style={{ animationDelay: '2.2s' }} />
          </div>
        )}
      </div>

        {/* Main Content Area */}
        <div
          className="relative z-20 transition-all duration-300"
          style={{
            marginLeft: sidebarCollapsed ? '80px' : '280px',
            marginTop: '64px',
            height: 'calc(100vh - 64px)',
            width: chatPanelOpen
              ? sidebarCollapsed ? 'calc(100% - 80px - 384px)' : 'calc(100% - 280px - 384px)'
              : sidebarCollapsed ? 'calc(100% - 80px)' : 'calc(100% - 280px)'
          }}
        >
          <div className="h-full overflow-y-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="p-6"
            >
              {children}
            </motion.div>
          </div>
        </div>

        {/* QuantumLink Chat Panel */}
        {/* <QuantumLinkPanel
          isOpen={chatPanelOpen}
          onToggle={() => setChatPanelOpen(!chatPanelOpen)}
        /> */}
      </div>
        </ChatProvider>
      </AuthProvider>
    </ClientOnlyWrapper>
  )
}
