import { useMemo } from 'react'
import { usePlayerData } from '../../store/dashboardStore'
import { EvolutionTheme } from '../types'

export function useEvolutionTheme(): EvolutionTheme {
  const playerData = usePlayerData()

  return useMemo(() => {
    const stage = playerData?.level || 1
    
    if (stage <= 2) return {
      primary: 'rgba(34, 211, 238, 1)', // Neural cyan
      secondary: 'rgba(139, 92, 246, 1)', // Quantum purple
      glow: 'rgba(34, 211, 238, 0.3)'
    }
    
    if (stage <= 5) return {
      primary: 'rgba(139, 92, 246, 1)', // Quantum purple
      secondary: 'rgba(245, 158, 11, 1)', // Flame orange
      glow: 'rgba(139, 92, 246, 0.3)'
    }
    
    if (stage <= 8) return {
      primary: 'rgba(245, 158, 11, 1)', // Flame orange
      secondary: 'rgba(239, 68, 68, 1)', // Red
      glow: 'rgba(245, 158, 11, 0.3)'
    }
    
    return {
      primary: 'rgba(255, 215, 0, 1)', // Gold
      secondary: 'rgba(255, 255, 255, 1)', // White
      glow: 'rgba(255, 215, 0, 0.4)'
    }
  }, [playerData?.level])
}
